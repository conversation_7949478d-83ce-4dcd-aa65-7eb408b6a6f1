package com.connect4.mod.events;

import com.connect4.mod.game.Connect4Game;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.ActionResultType;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.World;
import net.minecraftforge.event.entity.player.PlayerInteractEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

public class Connect4EventHandler {

    @SubscribeEvent
    public void onBlockRightClick(PlayerInteractEvent.RightClickBlock event) {
        PlayerEntity player = event.getPlayer();
        World world = event.getWorld();
        BlockPos pos = event.getPos();
        Hand hand = event.getHand();

        // Only handle main hand interactions
        if (hand != Hand.MAIN_HAND) {
            return;
        }

        // Only handle server-side
        if (world.isClientSide) {
            return;
        }

        // Check if player is in a Connect 4 game
        Connect4Game game = Connect4Game.getPlayerGame(player);
        if (game == null) {
            return;
        }

        // Check if the clicked block is a glowstone (column indicator)
        if (world.getBlockState(pos).getBlock() == Blocks.GLOWSTONE) {
            // Try to determine which column was clicked
            int column = game.getColumnFromPosition(pos);
            if (column >= 0 && column < 7) {
                // Check if it's the player's turn (skip check for solo mode)
                if (game.getCurrentPlayer() != player && !game.isSoloMode()) {
                    player.sendMessage(new StringTextComponent(
                        TextFormatting.RED + "It's not your turn!"), player.getUUID());
                    event.setCancellationResult(ActionResultType.SUCCESS);
                    event.setCanceled(true);
                    return;
                }

                // Try to drop a piece
                if (game.dropPiece(column)) {
                    player.sendMessage(new StringTextComponent(
                        TextFormatting.GREEN + "Piece dropped in column " + (column + 1) + "!"),
                        player.getUUID());
                } else {
                    player.sendMessage(new StringTextComponent(
                        TextFormatting.RED + "Column " + (column + 1) + " is full!"),
                        player.getUUID());
                }

                event.setCancellationResult(ActionResultType.SUCCESS);
                event.setCanceled(true);
            }
        }
    }

    @SubscribeEvent
    public void onPlayerLogout(net.minecraftforge.event.entity.player.PlayerEvent.PlayerLoggedOutEvent event) {
        PlayerEntity player = event.getPlayer();
        Connect4Game game = Connect4Game.getPlayerGame(player);

        if (game != null) {
            // End the game when a player logs out
            game.endGame();

            // Notify the other player
            PlayerEntity otherPlayer = (game.getPlayer1() == player) ? game.getPlayer2() : game.getPlayer1();
            if (otherPlayer instanceof ServerPlayerEntity) {
                otherPlayer.sendMessage(new StringTextComponent(
                    TextFormatting.YELLOW + player.getDisplayName().getString() +
                    " left the game. Game ended."), otherPlayer.getUUID());
            }
        }
    }
}
