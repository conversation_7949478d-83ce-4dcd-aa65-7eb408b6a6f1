package com.connect4.mod.events;

import com.connect4.mod.game.Connect4Game;
import com.connect4.mod.game.TicTacToeGame;
import com.connect4.mod.game.SlotMachineGame;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.ActionResultType;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.World;
import net.minecraftforge.event.entity.player.PlayerInteractEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

public class Connect4EventHandler {

    @SubscribeEvent
    public void onBlockRightClick(PlayerInteractEvent.RightClickBlock event) {
        PlayerEntity player = event.getPlayer();
        World world = event.getWorld();
        BlockPos pos = event.getPos();
        Hand hand = event.getHand();

        // Only handle main hand interactions
        if (hand != Hand.MAIN_HAND) {
            return;
        }

        // Only handle server-side
        if (world.isClientSide) {
            return;
        }

        // Check if player is in any game
        Connect4Game connect4Game = Connect4Game.getPlayerGame(player);
        TicTacToeGame ticTacToeGame = TicTacToeGame.getPlayerGame(player);
        SlotMachineGame slotMachineGame = SlotMachineGame.getPlayerGame(player);

        if (connect4Game != null) {
            // Handle Connect 4 game interactions
            // Check if the clicked block is a glowstone (column indicator)
            if (world.getBlockState(pos).getBlock() == Blocks.GLOWSTONE) {
                // Try to determine which column was clicked
                int column = connect4Game.getColumnFromPosition(pos);
                if (column >= 0 && column < 7) {
                    // Check if it's the player's turn (skip check for solo mode)
                    if (connect4Game.getCurrentPlayer() != player && !connect4Game.isSoloMode()) {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.RED + "It's not your turn!"), player.getUUID());
                        event.setCancellationResult(ActionResultType.SUCCESS);
                        event.setCanceled(true);
                        return;
                    }

                    // Try to drop a piece
                    if (connect4Game.dropPiece(column)) {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.GREEN + "Piece dropped in column " + (column + 1) + "!"),
                            player.getUUID());
                    } else {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.RED + "Column " + (column + 1) + " is full!"),
                            player.getUUID());
                    }

                    event.setCancellationResult(ActionResultType.SUCCESS);
                    event.setCanceled(true);
                }
            }
        } else if (ticTacToeGame != null) {
            // Handle Tic Tac Toe game interactions
            // Check if the clicked block is a glowstone (position indicator) or direct board click
            if (world.getBlockState(pos).getBlock() == Blocks.GLOWSTONE) {
                int[] position = ticTacToeGame.getPositionFromBlockPos(pos);
                if (position[0] >= 0 && position[1] >= 0) {
                    // Check if it's the player's turn (skip check for solo mode)
                    if (ticTacToeGame.getCurrentPlayer() != player && !ticTacToeGame.isSoloMode()) {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.RED + "It's not your turn!"), player.getUUID());
                        event.setCancellationResult(ActionResultType.SUCCESS);
                        event.setCanceled(true);
                        return;
                    }

                    // For tic tac toe, clicking glowstone should prompt for Y coordinate
                    player.sendMessage(new StringTextComponent(
                        TextFormatting.YELLOW + "Use /tictactoe place " + (position[0] + 1) + " <y> to place your piece!"),
                        player.getUUID());

                    event.setCancellationResult(ActionResultType.SUCCESS);
                    event.setCanceled(true);
                }
            }
            // Check if clicking directly on the game board area
            else if (world.getBlockState(pos).getBlock() == Blocks.YELLOW_CONCRETE ||
                     world.getBlockState(pos).getBlock() == Blocks.AIR) {
                int[] position = ticTacToeGame.getPositionFromBlockPos(pos);
                if (position[0] >= 0 && position[1] >= 0) {
                    // Check if it's the player's turn (skip check for solo mode)
                    if (ticTacToeGame.getCurrentPlayer() != player && !ticTacToeGame.isSoloMode()) {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.RED + "It's not your turn!"), player.getUUID());
                        event.setCancellationResult(ActionResultType.SUCCESS);
                        event.setCanceled(true);
                        return;
                    }

                    // Try to place a piece
                    if (ticTacToeGame.placePiece(position[0], position[1])) {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.GREEN + "Piece placed at (" + (position[0] + 1) + "," + (position[1] + 1) + ")!"),
                            player.getUUID());
                    } else {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.RED + "Could not place piece at (" + (position[0] + 1) + "," + (position[1] + 1) + ")!"),
                            player.getUUID());
                    }

                    event.setCancellationResult(ActionResultType.SUCCESS);
                    event.setCanceled(true);
                }
            }
        } else if (slotMachineGame != null) {
            // Handle Slot Machine game interactions
            // Check if the clicked block is a lever
            if (world.getBlockState(pos).getBlock() == Blocks.LEVER) {
                if (slotMachineGame.isLeverPosition(pos)) {
                    if (!slotMachineGame.isGameActive()) {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.RED + "The game is not active!"), player.getUUID());
                        event.setCancellationResult(ActionResultType.SUCCESS);
                        event.setCanceled(true);
                        return;
                    }

                    if (slotMachineGame.isSpinning()) {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.YELLOW + "The slots are already spinning! Wait for the result..."), player.getUUID());
                        event.setCancellationResult(ActionResultType.SUCCESS);
                        event.setCanceled(true);
                        return;
                    }

                    // Pull the lever!
                    if (slotMachineGame.pullLever()) {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.GOLD + "🎰 Pulling the lever... Good luck!"),
                            player.getUUID());
                    } else {
                        player.sendMessage(new StringTextComponent(
                            TextFormatting.RED + "Could not pull the lever!"),
                            player.getUUID());
                    }

                    event.setCancellationResult(ActionResultType.SUCCESS);
                    event.setCanceled(true);
                }
            }
            // Also allow clicking the glowstone indicator as an alternative
            else if (world.getBlockState(pos).getBlock() == Blocks.GLOWSTONE) {
                player.sendMessage(new StringTextComponent(
                    TextFormatting.YELLOW + "Click the lever to spin the slots! Or use /slotmachine spin"),
                    player.getUUID());

                event.setCancellationResult(ActionResultType.SUCCESS);
                event.setCanceled(true);
            }
        }
    }

    @SubscribeEvent
    public void onPlayerLogout(net.minecraftforge.event.entity.player.PlayerEvent.PlayerLoggedOutEvent event) {
        PlayerEntity player = event.getPlayer();
        Connect4Game connect4Game = Connect4Game.getPlayerGame(player);
        TicTacToeGame ticTacToeGame = TicTacToeGame.getPlayerGame(player);
        SlotMachineGame slotMachineGame = SlotMachineGame.getPlayerGame(player);

        if (connect4Game != null) {
            // End the Connect 4 game when a player logs out
            connect4Game.endGame();

            // Notify the other player
            PlayerEntity otherPlayer = (connect4Game.getPlayer1() == player) ? connect4Game.getPlayer2() : connect4Game.getPlayer1();
            if (otherPlayer instanceof ServerPlayerEntity) {
                otherPlayer.sendMessage(new StringTextComponent(
                    TextFormatting.YELLOW + player.getDisplayName().getString() +
                    " left the Connect 4 game. Game ended."), otherPlayer.getUUID());
            }
        }

        if (ticTacToeGame != null) {
            // End the Tic Tac Toe game when a player logs out
            ticTacToeGame.endGame();

            // Notify the other player
            PlayerEntity otherPlayer = (ticTacToeGame.getPlayer1() == player) ? ticTacToeGame.getPlayer2() : ticTacToeGame.getPlayer1();
            if (otherPlayer instanceof ServerPlayerEntity) {
                otherPlayer.sendMessage(new StringTextComponent(
                    TextFormatting.YELLOW + player.getDisplayName().getString() +
                    " left the Tic Tac Toe game. Game ended."), otherPlayer.getUUID());
            }
        }

        if (slotMachineGame != null) {
            // End the Slot Machine game when a player logs out
            slotMachineGame.endGame();
        }
    }
}
