package com.connect4.mod.game;

import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.particles.ParticleTypes;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvents;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.World;
import net.minecraft.world.server.ServerWorld;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class TicTacToeGame {
    private static final int BOARD_SIZE = 3;

    // Game state
    private Block[][] board;
    private BlockPos gameOrigin;
    private World world;
    private PlayerEntity player1;
    private PlayerEntity player2;
    private PlayerEntity currentPlayer;
    private boolean gameActive;
    private boolean gameWon;
    private PlayerEntity winner;
    private boolean soloMode;

    // Visual blocks for the game
    private static final Block PLAYER1_BLOCK = Blocks.RED_CONCRETE;
    private static final Block PLAYER2_BLOCK = Blocks.BLUE_CONCRETE;
    private static final Block EMPTY_BLOCK = Blocks.AIR;
    private static final Block BOARD_FRAME = Blocks.YELLOW_CONCRETE;

    // Static game instances for multiplayer support
    private static Map<UUID, TicTacToeGame> playerGames = new HashMap<>();

    public TicTacToeGame(World world, BlockPos origin, PlayerEntity player1, PlayerEntity player2) {
        this(world, origin, player1, player2, false);
    }

    public TicTacToeGame(World world, BlockPos origin, PlayerEntity player1, PlayerEntity player2, boolean soloMode) {
        this.world = world;
        this.gameOrigin = origin;
        this.player1 = player1;
        this.player2 = player2;
        this.currentPlayer = player1;
        this.gameActive = true;
        this.gameWon = false;
        this.soloMode = soloMode;
        this.board = new Block[BOARD_SIZE][BOARD_SIZE];

        // Initialize empty board
        for (int x = 0; x < BOARD_SIZE; x++) {
            for (int z = 0; z < BOARD_SIZE; z++) {
                board[x][z] = EMPTY_BLOCK;
            }
        }

        // Store game instances for both players
        playerGames.put(player1.getUUID(), this);
        if (!soloMode) {
            playerGames.put(player2.getUUID(), this);
        }

        buildGameBoard();
        if (soloMode) {
            sendGameMessage("Solo Tic Tac Toe game started! You control both X (RED) and O (BLUE) pieces.");
            sendGameMessage("Current turn: " + (currentPlayer == player1 ? "X (RED)" : "O (BLUE)"));
        } else {
            sendGameMessage("Tic Tac Toe game started! " + player1.getDisplayName().getString() + " (X/RED) vs " + player2.getDisplayName().getString() + " (O/BLUE)");
            sendGameMessage(currentPlayer.getDisplayName().getString() + "'s turn!");
        }
    }

    public static TicTacToeGame getPlayerGame(PlayerEntity player) {
        return playerGames.get(player.getUUID());
    }

    public static void removePlayerGame(PlayerEntity player) {
        TicTacToeGame game = playerGames.get(player.getUUID());
        if (game != null) {
            playerGames.remove(game.player1.getUUID());
            playerGames.remove(game.player2.getUUID());
        }
    }

    public BlockPos getGameOrigin() {
        return gameOrigin;
    }

    public int[] getPositionFromBlockPos(BlockPos pos) {
        int relativeX = pos.getX() - gameOrigin.getX();
        int relativeZ = pos.getZ() - gameOrigin.getZ();

        // Check if clicking on the glowstone indicators (at z = -7)
        if (relativeZ == -7 && relativeX >= 0 && relativeX < BOARD_SIZE) {
            return new int[]{relativeX, 0}; // Row selection via glowstone
        }

        // Check if clicking directly on the game board (z = -2)
        if (relativeZ == -2 && relativeX >= 0 && relativeX < BOARD_SIZE) {
            // For tic tac toe, we need both X and Z coordinates
            int relativeY = pos.getY() - gameOrigin.getY();
            if (relativeY >= 0 && relativeY < BOARD_SIZE) {
                return new int[]{relativeX, relativeY};
            }
        }

        return new int[]{-1, -1}; // Invalid position
    }

    public void buildGameBoard() {
        // Clear the entire game area first (expanded for player space and closer backdrop)
        for (int x = -3; x <= BOARD_SIZE + 2; x++) {
            for (int y = -2; y <= BOARD_SIZE + 3; y++) {
                for (int z = -11; z <= 2; z++) {
                    BlockPos pos = gameOrigin.offset(x, y, z);
                    world.setBlockAndUpdate(pos, EMPTY_BLOCK.defaultBlockState());
                }
            }
        }

        // Build only the base platform (bottom frame) - moved 2 blocks closer (z-2)
        for (int x = -1; x <= BOARD_SIZE; x++) {
            for (int z = -3; z <= -1; z++) {
                BlockPos pos = gameOrigin.offset(x, -1, z);
                world.setBlockAndUpdate(pos, BOARD_FRAME.defaultBlockState());
            }
        }

        // Build side pillars (left and right) - moved 2 blocks closer (z-2)
        for (int y = 0; y <= BOARD_SIZE; y++) {
            // Left pillar
            BlockPos leftPos = gameOrigin.offset(-1, y, -2);
            world.setBlockAndUpdate(leftPos, BOARD_FRAME.defaultBlockState());

            // Right pillar
            BlockPos rightPos = gameOrigin.offset(BOARD_SIZE, y, -2);
            world.setBlockAndUpdate(rightPos, BOARD_FRAME.defaultBlockState());
        }

        // Add yellow concrete roof on top of the Tic Tac Toe stage
        for (int x = -1; x <= BOARD_SIZE; x++) {
            BlockPos roofPos = gameOrigin.offset(x, BOARD_SIZE + 1, -2);
            world.setBlockAndUpdate(roofPos, BOARD_FRAME.defaultBlockState());
        }

        // Create extended player standing area (7 blocks away from the bottom)
        // Build a larger platform for the player to stand on
        for (int x = -2; x <= BOARD_SIZE + 1; x++) {
            for (int z = -10; z <= -5; z++) {
                BlockPos platformPos = gameOrigin.offset(x, -1, z);
                world.setBlockAndUpdate(platformPos, BOARD_FRAME.defaultBlockState());
            }
        }

        // Add raised edges around the player platform for a professional look
        // But skip the front edge where glowstone is to avoid blocking the view
        for (int x = -2; x <= BOARD_SIZE + 1; x++) {
            // Back edge
            BlockPos backEdge = gameOrigin.offset(x, 0, -10);
            world.setBlockAndUpdate(backEdge, BOARD_FRAME.defaultBlockState());
            // Skip front edge - no railing in front of glowstone to maintain clear view
        }
        for (int z = -10; z <= -5; z++) {
            // Left edge
            BlockPos leftEdge = gameOrigin.offset(-2, 0, z);
            world.setBlockAndUpdate(leftEdge, BOARD_FRAME.defaultBlockState());
            // Right edge
            BlockPos rightEdge = gameOrigin.offset(BOARD_SIZE + 1, 0, z);
            world.setBlockAndUpdate(rightEdge, BOARD_FRAME.defaultBlockState());
        }

        // Add position indicators at player level (7 blocks away from game board)
        for (int x = 0; x < BOARD_SIZE; x++) {
            BlockPos indicatorPos = gameOrigin.offset(x, 0, -7); // At player eye level
            world.setBlockAndUpdate(indicatorPos, Blocks.GLOWSTONE.defaultBlockState());
        }

        // Add number signs above the glowstone indicators for easy reference
        for (int x = 0; x < BOARD_SIZE; x++) {
            BlockPos signPos = gameOrigin.offset(x, 1, -7);
            world.setBlockAndUpdate(signPos, Blocks.OAK_SIGN.defaultBlockState());
        }

        // Add some lighting around the player area
        BlockPos leftLight = gameOrigin.offset(-1, 1, -7);
        BlockPos rightLight = gameOrigin.offset(BOARD_SIZE, 1, -7);
        world.setBlockAndUpdate(leftLight, Blocks.GLOWSTONE.defaultBlockState());
        world.setBlockAndUpdate(rightLight, Blocks.GLOWSTONE.defaultBlockState());

        // Create backdrop/back splash for the Tic Tac Toe board
        // Build a black concrete wall right next to the game board (moved 2 blocks closer: z=-1)
        for (int x = -1; x <= BOARD_SIZE; x++) {
            for (int y = -1; y <= BOARD_SIZE + 1; y++) {
                BlockPos backdropPos = gameOrigin.offset(x, y, -1);
                world.setBlockAndUpdate(backdropPos, Blocks.BLACK_CONCRETE.defaultBlockState());
            }
        }
    }

    public boolean placePiece(int x, int y) {
        if (!gameActive || gameWon || x < 0 || x >= BOARD_SIZE || y < 0 || y >= BOARD_SIZE) {
            return false;
        }

        // Check if position is already occupied
        if (board[x][y] != EMPTY_BLOCK) {
            sendGameMessage("Position (" + (x + 1) + "," + (y + 1) + ") is already occupied!");
            return false;
        }

        // Place the piece
        Block pieceBlock = (currentPlayer == player1) ? PLAYER1_BLOCK : PLAYER2_BLOCK;
        board[x][y] = pieceBlock;

        // Update the world (moved 2 blocks closer: z=-2)
        BlockPos piecePos = gameOrigin.offset(x, y, -2);
        world.setBlockAndUpdate(piecePos, pieceBlock.defaultBlockState());

        // Play sound effect
        world.playSound(null, piecePos, SoundEvents.STONE_PLACE, SoundCategory.BLOCKS, 1.0f, 1.0f);

        // Add particle effects
        if (world instanceof ServerWorld) {
            ServerWorld serverWorld = (ServerWorld) world;
            serverWorld.sendParticles(ParticleTypes.HAPPY_VILLAGER,
                piecePos.getX() + 0.5, piecePos.getY() + 1.0, piecePos.getZ() + 0.5,
                10, 0.5, 0.5, 0.5, 0.1);
        }

        // Check for win
        if (checkWin()) {
            gameWon = true;
            winner = currentPlayer;
            sendGameMessage(TextFormatting.GOLD + "🎉 " + winner.getDisplayName().getString() + " WINS! 🎉");
            celebrateWin();
            return true;
        }

        // Check for tie
        if (isBoardFull()) {
            gameWon = true;
            sendGameMessage(TextFormatting.YELLOW + "It's a tie! Good game!");
            return true;
        }

        // Switch players
        currentPlayer = (currentPlayer == player1) ? player2 : player1;
        String playerSymbol = (currentPlayer == player1) ? TextFormatting.RED + "X (RED)" : TextFormatting.BLUE + "O (BLUE)";

        if (soloMode) {
            sendGameMessage("Your turn: " + playerSymbol + TextFormatting.RESET + " pieces");
        } else {
            sendGameMessage(currentPlayer.getDisplayName().getString() + "'s turn! (" + playerSymbol + TextFormatting.RESET + ")");
        }

        return true;
    }

    private boolean checkWin() {
        // Check rows
        for (int y = 0; y < BOARD_SIZE; y++) {
            if (board[0][y] != EMPTY_BLOCK && board[0][y] == board[1][y] && board[1][y] == board[2][y]) {
                return true;
            }
        }

        // Check columns
        for (int x = 0; x < BOARD_SIZE; x++) {
            if (board[x][0] != EMPTY_BLOCK && board[x][0] == board[x][1] && board[x][1] == board[x][2]) {
                return true;
            }
        }

        // Check diagonals
        if (board[0][0] != EMPTY_BLOCK && board[0][0] == board[1][1] && board[1][1] == board[2][2]) {
            return true;
        }
        if (board[2][0] != EMPTY_BLOCK && board[2][0] == board[1][1] && board[1][1] == board[0][2]) {
            return true;
        }

        return false;
    }

    private boolean isBoardFull() {
        for (int x = 0; x < BOARD_SIZE; x++) {
            for (int y = 0; y < BOARD_SIZE; y++) {
                if (board[x][y] == EMPTY_BLOCK) {
                    return false;
                }
            }
        }
        return true;
    }

    private void celebrateWin() {
        if (world instanceof ServerWorld) {
            ServerWorld serverWorld = (ServerWorld) world;

            // Create firework-like particle effects around the board
            for (int i = 0; i < 30; i++) {
                double x = gameOrigin.getX() + Math.random() * BOARD_SIZE;
                double y = gameOrigin.getY() + Math.random() * BOARD_SIZE + 2;
                double z = gameOrigin.getZ() + (Math.random() - 0.5) * 3;

                serverWorld.sendParticles(ParticleTypes.FIREWORK, x, y, z, 1, 0, 0, 0, 0.1);
            }

            // Play victory sound
            world.playSound(null, gameOrigin, SoundEvents.UI_TOAST_CHALLENGE_COMPLETE,
                SoundCategory.BLOCKS, 2.0f, 1.0f);
        }
    }

    public void resetGame() {
        // Clear the board (moved 2 blocks closer: z=-2)
        for (int x = 0; x < BOARD_SIZE; x++) {
            for (int y = 0; y < BOARD_SIZE; y++) {
                board[x][y] = EMPTY_BLOCK;
                BlockPos pos = gameOrigin.offset(x, y, -2);
                world.setBlockAndUpdate(pos, EMPTY_BLOCK.defaultBlockState());
            }
        }

        // Reset game state
        currentPlayer = player1;
        gameWon = false;
        winner = null;

        sendGameMessage("Game reset! " + (soloMode ? "X (RED)" : currentPlayer.getDisplayName().getString()) + "'s turn!");
    }

    public void endGame() {
        // Clear the entire game area (expanded to match buildGameBoard including closer backdrop)
        for (int x = -3; x <= BOARD_SIZE + 2; x++) {
            for (int y = -2; y <= BOARD_SIZE + 3; y++) {
                for (int z = -11; z <= 2; z++) {
                    BlockPos pos = gameOrigin.offset(x, y, z);
                    world.setBlockAndUpdate(pos, EMPTY_BLOCK.defaultBlockState());
                }
            }
        }

        gameActive = false;
        removePlayerGame(player1);
        sendGameMessage("Game ended!");
    }

    private void sendGameMessage(String message) {
        player1.sendMessage(new StringTextComponent("[TicTacToe] " + message), player1.getUUID());
        if (!soloMode && player2 != player1) {
            player2.sendMessage(new StringTextComponent("[TicTacToe] " + message), player2.getUUID());
        }
    }

    // Getters
    public boolean isGameActive() { return gameActive; }
    public boolean isGameWon() { return gameWon; }
    public PlayerEntity getCurrentPlayer() { return currentPlayer; }
    public PlayerEntity getWinner() { return winner; }
    public PlayerEntity getPlayer1() { return player1; }
    public PlayerEntity getPlayer2() { return player2; }
    public boolean isSoloMode() { return soloMode; }
}
