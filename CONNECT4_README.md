# Connect 4 Gamemode Mod for Minecraft 1.16.5

A visually appealing Connect 4 gamemode mod perfect for YouTube content creation!

## Features

🎮 **Interactive Gameplay**
- Full Connect 4 game mechanics with win detection
- Visual game board with colored concrete blocks (Red vs Blue)
- Click-to-play interface using glowstone column indicators
- Turn-based gameplay with clear player indicators

🎨 **Visual Effects**
- Particle effects when pieces are dropped
- Celebration fireworks when someone wins
- Color-coded game pieces (Red and Blue concrete)
- Yellow concrete frame for the game board
- Glowstone column indicators for easy clicking

🔊 **Audio Feedback**
- Sound effects for piece placement
- Victory sounds when someone wins
- Audio cues for game events

⚡ **Easy Commands**
- Simple command system for quick game setup
- Perfect for content creators and streamers
- Reset and end game functionality

## Commands

### Basic Commands
- `/connect4 start <player>` - Start a new game with another player
- `/connect4 start` - Start a solo game (play against yourself!)
- `/connect4 drop <column>` - Drop a piece in column 1-7
- `/connect4 reset` - Reset the current game
- `/connect4 end` - End the current game
- `/connect4 status` - Check game status
- `/connect4 help` - Show help message

### How to Play

1. **Start a Game**:
   - **Multiplayer**: `/connect4 start <playername>` to challenge another player
   - **Solo Mode**: `/connect4 start` to play against yourself (perfect for demos!)
2. **Drop Pieces**: Either use `/connect4 drop <column>` or click the glowstone blocks above each column
3. **Win Condition**: Get 4 pieces in a row (horizontal, vertical, or diagonal)
4. **Reset/End**: Use `/connect4 reset` to play again or `/connect4 end` to stop

## Perfect for YouTube Content!

This mod is specifically designed for content creation:

### Visual Appeal
- Bright, contrasting colors (Red vs Blue)
- Clear game board layout
- Satisfying particle effects
- Victory celebrations with fireworks

### Easy Recording Setup
- Quick game setup with simple commands
- Automatic game board construction
- Clear visual feedback for viewers
- Professional-looking game presentation

### Content Ideas
- **Quick Matches**: Perfect for YouTube Shorts format
- **Tournament Style**: Multiple rounds with different players
- **Challenge Videos**: Speed rounds or special rules
- **Collaborative Content**: Team up with other creators

## Installation

1. Make sure you have Minecraft 1.16.5 and Forge 36.2.42 installed
2. Build the mod using `./gradlew build`
3. Place the generated JAR file in your mods folder
4. Launch Minecraft with Forge

## Building the Mod

```bash
# Windows
gradlew.bat build

# Linux/Mac
./gradlew build
```

The built mod will be in `build/libs/`

## Technical Details

- **Minecraft Version**: 1.16.5
- **Forge Version**: 36.2.42
- **Mod ID**: connect4mod
- **Package**: com.connect4.mod

## Game Mechanics

- **Board Size**: 7 columns × 6 rows (standard Connect 4)
- **Win Condition**: 4 pieces in a row (any direction)
- **Turn System**: Alternating turns between players
- **Piece Physics**: Pieces drop to the lowest available position
- **Visual Feedback**: Immediate block placement and particle effects

## Tips for Content Creation

1. **Camera Angles**: Position yourself to show the full game board
2. **Lighting**: The glowstone indicators provide good lighting
3. **Commentary**: Explain your strategy while playing
4. **Editing**: The quick gameplay is perfect for fast-paced editing
5. **Thumbnails**: The colorful game board makes great thumbnail material

## Troubleshooting

- **Can't start game**: Make sure both players are online and not in another game
- **Column clicking not working**: Use the command `/connect4 drop <column>` instead
- **Game board not appearing**: Check that you have enough space (9×8×3 blocks)

## Future Enhancements

- Spectator mode for tournaments
- Custom game board themes
- Statistics tracking
- Replay system
- AI opponent option

---

**Created for YouTube content creators who want engaging, visually appealing Minecraft gameplay!**
