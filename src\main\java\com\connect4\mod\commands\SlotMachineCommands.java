package com.connect4.mod.commands;

import com.connect4.mod.game.SlotMachineGame;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.command.CommandSource;
import net.minecraft.command.Commands;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;

public class SlotMachineCommands {

    public static void register(CommandDispatcher<CommandSource> dispatcher) {
        dispatcher.register(Commands.literal("slotmachine")
            .then(Commands.literal("start")
                .executes(SlotMachineCommands::startGame))
            .then(Commands.literal("spin")
                .executes(SlotMachineCommands::spinSlots))
            .then(Commands.literal("end")
                .executes(SlotMachineCommands::endGame))
            .then(Commands.literal("status")
                .executes(SlotMachineCommands::gameStatus))
            .then(Commands.literal("help")
                .executes(SlotMachineCommands::showHelp)));
    }

    private static int startGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can start Slot Machine games!"));
            return 0;
        }

        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();

        // Check if player is already in a game
        if (SlotMachineGame.getPlayerGame(player) != null) {
            context.getSource().sendFailure(new StringTextComponent("You are already in a Slot Machine game! Use /slotmachine end to leave."));
            return 0;
        }

        // Create game at player's location
        BlockPos gamePos = player.blockPosition().offset(3, 0, 0);
        new SlotMachineGame(player.level, gamePos, player);

        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.GOLD + "🎰 Slot Machine Challenge started! " +
            TextFormatting.RED + "⚠️ WARNING: Get 3 diamonds for OP gear, otherwise... BOOM! 💥"), true);

        return 1;
    }

    private static int spinSlots(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can spin slots!"));
            return 0;
        }

        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        SlotMachineGame game = SlotMachineGame.getPlayerGame(player);

        if (game == null) {
            context.getSource().sendFailure(new StringTextComponent("You are not in a Slot Machine game! Use /slotmachine start to start one."));
            return 0;
        }

        if (!game.isGameActive()) {
            context.getSource().sendFailure(new StringTextComponent("The game is not active!"));
            return 0;
        }

        if (game.isSpinning()) {
            context.getSource().sendFailure(new StringTextComponent("The slots are already spinning! Wait for the result..."));
            return 0;
        }

        if (game.pullLever()) {
            context.getSource().sendSuccess(new StringTextComponent(
                TextFormatting.YELLOW + "🎰 Pulling the lever... Good luck!"), false);
            return 1;
        } else {
            context.getSource().sendFailure(new StringTextComponent("Could not spin the slots!"));
            return 0;
        }
    }

    private static int endGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can end games!"));
            return 0;
        }

        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        SlotMachineGame game = SlotMachineGame.getPlayerGame(player);

        if (game == null) {
            context.getSource().sendFailure(new StringTextComponent("You are not in a Slot Machine game!"));
            return 0;
        }

        game.endGame();
        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.RED + "Slot Machine Challenge ended!"), true);

        return 1;
    }

    private static int gameStatus(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can check game status!"));
            return 0;
        }

        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        SlotMachineGame game = SlotMachineGame.getPlayerGame(player);

        if (game == null) {
            context.getSource().sendSuccess(new StringTextComponent(
                TextFormatting.GRAY + "You are not in a Slot Machine game."), false);
            return 1;
        }

        String status = TextFormatting.GOLD + "=== 🎰 Slot Machine Status 🎰 ===\n";
        status += TextFormatting.WHITE + "Player: " + game.getPlayer().getDisplayName().getString() + "\n";
        status += TextFormatting.WHITE + "Game Active: " + (game.isGameActive() ? TextFormatting.GREEN + "Yes" : TextFormatting.RED + "No") + "\n";
        status += TextFormatting.WHITE + "Currently Spinning: " + (game.isSpinning() ? TextFormatting.YELLOW + "Yes" : TextFormatting.GREEN + "No") + "\n";
        status += TextFormatting.WHITE + "Current Results: " + game.getSlotResultsString() + "\n";
        status += TextFormatting.YELLOW + "Goal: Get 💎 | 💎 | 💎 for OP gear!\n";
        status += TextFormatting.RED + "Warning: Anything else = BOOM! 💥";

        context.getSource().sendSuccess(new StringTextComponent(status), false);
        return 1;
    }

    private static int showHelp(CommandContext<CommandSource> context) {
        String help = TextFormatting.GOLD + "=== 🎰 Slot Machine Commands 🎰 ===\n" +
                     TextFormatting.WHITE + "/slotmachine start - Start a new Slot Machine Challenge\n" +
                     "/slotmachine end - End the current game\n" +
                     "/slotmachine status - Check game status and results\n" +
                     "/slotmachine help - Show this help message\n\n" +
                     TextFormatting.YELLOW + "🎯 How to play:\n" +
                     TextFormatting.WHITE + "- Start the challenge with /slotmachine start\n" +
                     TextFormatting.GOLD + "- CLICK THE LEVER on the right side to spin!\n" +
                     TextFormatting.WHITE + "- Get 3 diamonds (💎 | 💎 | 💎) to win OP gear!\n" +
                     "- Get anything else and... " + TextFormatting.RED + "BOOM! 💥\n" +
                     TextFormatting.GREEN + "- Perfect for high-stakes YouTube content!\n\n" +
                     TextFormatting.GOLD + "🎰 Slot Symbols: 💎 🟨 ⚪ 🟢 🔴 ⚫\n" +
                     TextFormatting.RED + "⚠️ WARNING: Only diamonds save you from explosion!\n" +
                     TextFormatting.YELLOW + "💡 TIP: Click the lever block - no commands needed!";

        context.getSource().sendSuccess(new StringTextComponent(help), false);
        return 1;
    }
}
