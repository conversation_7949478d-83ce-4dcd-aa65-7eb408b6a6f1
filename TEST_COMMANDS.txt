# Connect 4 Mod - Test Commands

## Basic Testing Sequence

1. First, check if the mod loaded correctly:
   /connect4 help

2. Start a game:
   # For multiplayer (replace "TestPlayer" with actual player name):
   /connect4 start TestPlayer

   # OR for solo mode (play against yourself):
   /connect4 start

3. Test dropping pieces in different columns:
   /connect4 drop 1
   /connect4 drop 2
   /connect4 drop 3
   /connect4 drop 4
   /connect4 drop 5
   /connect4 drop 6
   /connect4 drop 7

4. Check game status:
   /connect4 status

5. Reset the game to test again:
   /connect4 reset

6. End the game when done:
   /connect4 end

## Quick Win Test (for demonstration)
# Solo mode - alternate between red and blue:
/connect4 start              # Start solo game
/connect4 drop 1             # Red piece
/connect4 drop 1             # Blue piece
/connect4 drop 2             # Red piece
/connect4 drop 2             # Blue piece
/connect4 drop 3             # Red piece
/connect4 drop 3             # Blue piece
/connect4 drop 4             # Red piece - should win horizontally!

## Visual Test Checklist
- [ ] Game board appears with yellow concrete frame
- [ ] Glowstone blocks appear above each column
- [ ] Red concrete appears for player 1 pieces
- [ ] Blue concrete appears for player 2 pieces
- [ ] Particle effects show when pieces are dropped
- [ ] Sound effects play on piece placement
- [ ] Win detection works correctly
- [ ] Firework particles appear on win
- [ ] Victory sound plays on win

## Error Testing
- Try starting a game with yourself: /connect4 start YourOwnName
- Try dropping in a full column
- Try playing when it's not your turn
- Try commands when not in a game

## Performance Test
- Start multiple games (if you have multiple players)
- Test game cleanup when players disconnect
- Test reset functionality multiple times
