package com.connect4.mod.commands;

import com.connect4.mod.game.FallTrustGame;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.command.CommandSource;
import net.minecraft.command.Commands;
import net.minecraft.command.arguments.EntityArgument;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;

public class FallTrustCommands {
    
    public static void register(CommandDispatcher<CommandSource> dispatcher) {
        dispatcher.register(Commands.literal("falltrust")
            .then(Commands.literal("start")
                .then(Commands.argument("player2", EntityArgument.player())
                    .executes(FallTrustCommands::startGame)))
            .then(Commands.literal("choose")
                .then(Commands.argument("x", IntegerArgumentType.integer(1, 5))
                    .then(Commands.argument("z", IntegerArgumentType.integer(1, 5))
                        .executes(FallTrustCommands::makeChoice))))
            .then(Commands.literal("end")
                .executes(FallTrustCommands::endGame))
            .then(Commands.literal("status")
                .executes(FallTrustCommands::gameStatus))
            .then(Commands.literal("help")
                .executes(FallTrustCommands::showHelp)));
    }
    
    private static int startGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can start Fall Trust games!"));
            return 0;
        }
        
        ServerPlayerEntity player1 = (ServerPlayerEntity) context.getSource().getEntity();
        ServerPlayerEntity player2 = EntityArgument.getPlayer(context, "player2");
        
        if (player1.equals(player2)) {
            context.getSource().sendFailure(new StringTextComponent("You can't play against yourself! This game requires trust between two players."));
            return 0;
        }
        
        // Check if either player is already in a game
        if (FallTrustGame.getPlayerGame(player1) != null) {
            context.getSource().sendFailure(new StringTextComponent("You are already in a Fall Trust game! Use /falltrust end to leave."));
            return 0;
        }
        
        if (FallTrustGame.getPlayerGame(player2) != null) {
            context.getSource().sendFailure(new StringTextComponent(player2.getDisplayName().getString() + " is already in a Fall Trust game!"));
            return 0;
        }
        
        // Create game at player's location
        BlockPos gamePos = player1.blockPosition().offset(5, 0, 0);
        new FallTrustGame(player1.level, gamePos, player1, player2);
        
        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.GOLD + "🕳️ Fall Trust Challenge started! " + 
            TextFormatting.RED + player1.getDisplayName().getString() + 
            TextFormatting.WHITE + " vs " + 
            TextFormatting.BLUE + player2.getDisplayName().getString() + 
            TextFormatting.WHITE + " - Who can you trust?"), true);
        
        return 1;
    }
    
    private static int makeChoice(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can make choices!"));
            return 0;
        }
        
        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        FallTrustGame game = FallTrustGame.getPlayerGame(player);
        
        if (game == null) {
            context.getSource().sendFailure(new StringTextComponent("You are not in a Fall Trust game! Use /falltrust start <player> to start one."));
            return 0;
        }
        
        if (game.isGameStarted()) {
            context.getSource().sendFailure(new StringTextComponent("The game has already started! Choices have been made."));
            return 0;
        }
        
        if (!game.isGameActive()) {
            context.getSource().sendFailure(new StringTextComponent("The game is not active!"));
            return 0;
        }
        
        int x = IntegerArgumentType.getInteger(context, "x");
        int z = IntegerArgumentType.getInteger(context, "z");
        
        if (game.makeChoice(player, x, z)) {
            context.getSource().sendSuccess(new StringTextComponent(
                TextFormatting.GREEN + "Choice made! You selected position (" + x + "," + z + ")"), false);
            return 1;
        } else {
            context.getSource().sendFailure(new StringTextComponent("Could not make choice!"));
            return 0;
        }
    }
    
    private static int endGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can end games!"));
            return 0;
        }
        
        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        FallTrustGame game = FallTrustGame.getPlayerGame(player);
        
        if (game == null) {
            context.getSource().sendFailure(new StringTextComponent("You are not in a Fall Trust game!"));
            return 0;
        }
        
        game.endGame();
        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.RED + "Fall Trust Challenge ended!"), true);
        
        return 1;
    }
    
    private static int gameStatus(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can check game status!"));
            return 0;
        }
        
        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        FallTrustGame game = FallTrustGame.getPlayerGame(player);
        
        if (game == null) {
            context.getSource().sendSuccess(new StringTextComponent(
                TextFormatting.GRAY + "You are not in a Fall Trust game."), false);
            return 1;
        }
        
        String status = TextFormatting.GOLD + "=== 🕳️ Fall Trust Status 🕳️ ===\n";
        status += TextFormatting.RED + "Player 1: " + game.getPlayer1().getDisplayName().getString() + "\n";
        status += TextFormatting.BLUE + "Player 2: " + game.getPlayer2().getDisplayName().getString() + "\n";
        status += TextFormatting.WHITE + "Game Active: " + (game.isGameActive() ? TextFormatting.GREEN + "Yes" : TextFormatting.RED + "No") + "\n";
        status += TextFormatting.WHITE + "Game Started: " + (game.isGameStarted() ? TextFormatting.YELLOW + "Yes" : TextFormatting.GREEN + "No") + "\n";
        
        if (!game.isGameStarted()) {
            status += TextFormatting.YELLOW + "Waiting for both players to choose their snow pile...\n";
            status += TextFormatting.WHITE + "Use /falltrust choose <x> <z> to make your choice (1-5, 1-5)";
        } else {
            status += TextFormatting.RED + "Game completed! Check the results above.";
        }
        
        context.getSource().sendSuccess(new StringTextComponent(status), false);
        return 1;
    }
    
    private static int showHelp(CommandContext<CommandSource> context) {
        String help = TextFormatting.GOLD + "=== 🕳️ Fall Trust Commands 🕳️ ===\n" +
                     TextFormatting.WHITE + "/falltrust start <player> - Start a new Fall Trust Challenge\n" +
                     "/falltrust choose <x> <z> - Choose your snow pile (1-5, 1-5)\n" +
                     "/falltrust end - End the current game\n" +
                     "/falltrust status - Check game status\n" +
                     "/falltrust help - Show this help message\n\n" +
                     TextFormatting.YELLOW + "🎯 How to play:\n" +
                     TextFormatting.WHITE + "- Two players face off on separate 5x5 platforms\n" +
                     "- Each platform has ONE deadly trap (void fall)\n" +
                     "- You can see the OTHER player's trap location\n" +
                     "- Tell each other where the trap is... truth or lie? 😈\n" +
                     "- Both players choose a snow pile to drop through\n" +
                     "- Fall into the void = lose, choose safely = survive!\n\n" +
                     TextFormatting.RED + "🧠 Psychology: Can you trust your opponent?\n" +
                     TextFormatting.GREEN + "Perfect for dramatic YouTube content!";
        
        context.getSource().sendSuccess(new StringTextComponent(help), false);
        return 1;
    }
}
