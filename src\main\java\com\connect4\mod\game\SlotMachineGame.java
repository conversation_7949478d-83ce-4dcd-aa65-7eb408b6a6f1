package com.connect4.mod.game;

import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.particles.ParticleTypes;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvents;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.World;
import net.minecraft.world.server.ServerWorld;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

public class SlotMachineGame {
    private static final int SLOT_COUNT = 3;

    // Game state
    private BlockPos gameOrigin;
    private World world;
    private PlayerEntity player;
    private boolean gameActive;
    private boolean spinning;
    private int spinTicks;
    private int[] slotResults;
    private Random random;

    // Visual blocks for the game
    private static final Block FRAME_BLOCK = Blocks.YELLOW_CONCRETE;
    private static final Block BACKDROP_BLOCK = Blocks.BLACK_CONCRETE;
    private static final Block SLOT_BLOCK = Blocks.REDSTONE_LAMP;

    // Slot symbols (represented by different blocks on top of redstone lamps)
    private static final Block[] SLOT_SYMBOLS = {
        Blocks.DIAMOND_BLOCK,    // 0 - Diamond (JACKPOT!)
        Blocks.GOLD_BLOCK,       // 1 - Gold
        Blocks.IRON_BLOCK,       // 2 - Iron
        Blocks.EMERALD_BLOCK,    // 3 - Emerald
        Blocks.REDSTONE_BLOCK,   // 4 - Redstone
        Blocks.COAL_BLOCK        // 5 - Coal
    };

    // Static game instances
    private static Map<UUID, SlotMachineGame> playerGames = new HashMap<>();

    public SlotMachineGame(World world, BlockPos origin, PlayerEntity player) {
        this.world = world;
        this.gameOrigin = origin;
        this.player = player;
        this.gameActive = true;
        this.spinning = false;
        this.spinTicks = 0;
        this.slotResults = new int[SLOT_COUNT];
        this.random = new Random();

        // Store game instance
        playerGames.put(player.getUUID(), this);

        buildSlotMachine();
        sendGameMessage("🎰 Slot Machine Challenge started! Pull the lever to spin!");
        sendGameMessage("⚠️ WARNING: Get 3 diamonds for OP gear, otherwise... BOOM! 💥");
    }

    public static SlotMachineGame getPlayerGame(PlayerEntity player) {
        return playerGames.get(player.getUUID());
    }

    public static void removePlayerGame(PlayerEntity player) {
        playerGames.remove(player.getUUID());
    }

    public BlockPos getGameOrigin() {
        return gameOrigin;
    }

    public boolean isLeverPosition(BlockPos pos) {
        // Check if clicked position is the lever (right side of the machine)
        int relativeX = pos.getX() - gameOrigin.getX();
        int relativeY = pos.getY() - gameOrigin.getY();
        int relativeZ = pos.getZ() - gameOrigin.getZ();

        // Lever is at position (4, 1, -2) relative to origin
        return relativeX == 4 && relativeY == 1 && relativeZ == -2;
    }

    public void buildSlotMachine() {
        // Clear the entire game area first
        for (int x = -3; x <= 6; x++) {
            for (int y = -2; y <= 5; y++) {
                for (int z = -11; z <= 2; z++) {
                    BlockPos pos = gameOrigin.offset(x, y, z);
                    world.setBlockAndUpdate(pos, Blocks.AIR.defaultBlockState());
                }
            }
        }

        // Build the slot machine base platform (moved 2 blocks closer: z-2)
        for (int x = -1; x <= 4; x++) {
            for (int z = -3; z <= -1; z++) {
                BlockPos pos = gameOrigin.offset(x, -1, z);
                world.setBlockAndUpdate(pos, FRAME_BLOCK.defaultBlockState());
            }
        }

        // Build the slot machine frame
        for (int y = 0; y <= 3; y++) {
            // Left and right pillars
            BlockPos leftPos = gameOrigin.offset(-1, y, -2);
            BlockPos rightPos = gameOrigin.offset(4, y, -2);
            world.setBlockAndUpdate(leftPos, FRAME_BLOCK.defaultBlockState());
            world.setBlockAndUpdate(rightPos, FRAME_BLOCK.defaultBlockState());
        }

        // Build the roof
        for (int x = -1; x <= 4; x++) {
            BlockPos roofPos = gameOrigin.offset(x, 4, -2);
            world.setBlockAndUpdate(roofPos, FRAME_BLOCK.defaultBlockState());
        }

        // Build the 3 redstone lamp slots
        for (int i = 0; i < SLOT_COUNT; i++) {
            BlockPos slotPos = gameOrigin.offset(i + 1, 2, -2);
            world.setBlockAndUpdate(slotPos, SLOT_BLOCK.defaultBlockState());

            // Add initial random symbol on top
            BlockPos symbolPos = gameOrigin.offset(i + 1, 3, -2);
            world.setBlockAndUpdate(symbolPos, SLOT_SYMBOLS[random.nextInt(SLOT_SYMBOLS.length)].defaultBlockState());
        }

        // Add the lever (using a lever block)
        BlockPos leverPos = gameOrigin.offset(4, 1, -2);
        world.setBlockAndUpdate(leverPos, Blocks.LEVER.defaultBlockState());

        // Create extended player standing area (7 blocks away from the machine)
        for (int x = -2; x <= 5; x++) {
            for (int z = -10; z <= -5; z++) {
                BlockPos platformPos = gameOrigin.offset(x, -1, z);
                world.setBlockAndUpdate(platformPos, FRAME_BLOCK.defaultBlockState());
            }
        }

        // Add raised edges around the player platform (skip front edge)
        for (int x = -2; x <= 5; x++) {
            // Back edge
            BlockPos backEdge = gameOrigin.offset(x, 0, -10);
            world.setBlockAndUpdate(backEdge, FRAME_BLOCK.defaultBlockState());
        }
        for (int z = -10; z <= -5; z++) {
            // Left and right edges
            BlockPos leftEdge = gameOrigin.offset(-2, 0, z);
            BlockPos rightEdge = gameOrigin.offset(5, 0, z);
            world.setBlockAndUpdate(leftEdge, FRAME_BLOCK.defaultBlockState());
            world.setBlockAndUpdate(rightEdge, FRAME_BLOCK.defaultBlockState());
        }

        // Add interaction indicator at player level
        BlockPos indicatorPos = gameOrigin.offset(2, 0, -7);
        world.setBlockAndUpdate(indicatorPos, Blocks.GLOWSTONE.defaultBlockState());

        // Add sign above indicator
        BlockPos signPos = gameOrigin.offset(2, 1, -7);
        world.setBlockAndUpdate(signPos, Blocks.OAK_SIGN.defaultBlockState());

        // Add side lighting
        BlockPos leftLight = gameOrigin.offset(0, 1, -7);
        BlockPos rightLight = gameOrigin.offset(4, 1, -7);
        world.setBlockAndUpdate(leftLight, Blocks.GLOWSTONE.defaultBlockState());
        world.setBlockAndUpdate(rightLight, Blocks.GLOWSTONE.defaultBlockState());

        // Create backdrop/back splash
        for (int x = -1; x <= 4; x++) {
            for (int y = -1; y <= 4; y++) {
                BlockPos backdropPos = gameOrigin.offset(x, y, -1);
                world.setBlockAndUpdate(backdropPos, BACKDROP_BLOCK.defaultBlockState());
            }
        }

        // Add decorative elements around the machine
        // Top sign
        BlockPos topSignPos = gameOrigin.offset(2, 5, -2);
        world.setBlockAndUpdate(topSignPos, Blocks.OAK_SIGN.defaultBlockState());

        // Side decorations
        BlockPos leftDecor = gameOrigin.offset(-1, 3, -2);
        BlockPos rightDecor = gameOrigin.offset(4, 3, -2);
        world.setBlockAndUpdate(leftDecor, Blocks.GOLD_BLOCK.defaultBlockState());
        world.setBlockAndUpdate(rightDecor, Blocks.GOLD_BLOCK.defaultBlockState());
    }

    public boolean pullLever() {
        if (!gameActive || spinning) {
            return false;
        }

        spinning = true;
        spinTicks = 0;

        // Generate random results for each slot
        for (int i = 0; i < SLOT_COUNT; i++) {
            slotResults[i] = random.nextInt(SLOT_SYMBOLS.length);
        }

        sendGameMessage("🎰 Spinning the slots... Good luck!");

        // Play spinning sound
        world.playSound(null, gameOrigin, SoundEvents.DISPENSER_DISPENSE, SoundCategory.BLOCKS, 1.0f, 0.5f);

        // Start the spinning animation
        startSpinAnimation();

        return true;
    }

    private void startSpinAnimation() {
        // This would be called repeatedly to animate the spinning
        // For now, we'll simulate the final result after a delay

        // Schedule the result after 3 seconds (60 ticks)
        // In a real implementation, you'd use a scheduler or tick handler
        world.getServer().execute(() -> {
            try {
                Thread.sleep(3000); // 3 second delay
                finishSpin();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }

    private void finishSpin() {
        if (!spinning) return;

        spinning = false;

        // Update the slot machine with final results
        for (int i = 0; i < SLOT_COUNT; i++) {
            BlockPos symbolPos = gameOrigin.offset(i + 1, 3, -2);
            world.setBlockAndUpdate(symbolPos, SLOT_SYMBOLS[slotResults[i]].defaultBlockState());
        }

        // Check results
        boolean jackpot = true;
        for (int result : slotResults) {
            if (result != 0) { // 0 = Diamond
                jackpot = false;
                break;
            }
        }

        if (jackpot) {
            // JACKPOT! Give OP gear
            handleJackpot();
        } else {
            // BOOM! Explosion
            handleExplosion();
        }
    }

    private void handleJackpot() {
        sendGameMessage(TextFormatting.GOLD + "🎉 JACKPOT! THREE DIAMONDS! 🎉");
        sendGameMessage(TextFormatting.GREEN + "You win OP gear and survive!");

        // Give OP gear to player
        if (player instanceof ServerPlayerEntity) {
            ServerPlayerEntity serverPlayer = (ServerPlayerEntity) player;

            // Give enchanted diamond gear
            ItemStack helmet = new ItemStack(Items.DIAMOND_HELMET);
            ItemStack chestplate = new ItemStack(Items.DIAMOND_CHESTPLATE);
            ItemStack leggings = new ItemStack(Items.DIAMOND_LEGGINGS);
            ItemStack boots = new ItemStack(Items.DIAMOND_BOOTS);
            ItemStack sword = new ItemStack(Items.DIAMOND_SWORD);
            ItemStack pickaxe = new ItemStack(Items.DIAMOND_PICKAXE);

            // Add to inventory
            serverPlayer.inventory.add(helmet);
            serverPlayer.inventory.add(chestplate);
            serverPlayer.inventory.add(leggings);
            serverPlayer.inventory.add(boots);
            serverPlayer.inventory.add(sword);
            serverPlayer.inventory.add(pickaxe);

            // Add some bonus items
            serverPlayer.inventory.add(new ItemStack(Items.DIAMOND, 64));
            serverPlayer.inventory.add(new ItemStack(Items.GOLDEN_APPLE, 16));
        }

        // Victory celebration
        celebrateJackpot();

        gameActive = false;
    }

    private void handleExplosion() {
        sendGameMessage(TextFormatting.RED + "💥 NO JACKPOT! BOOM! 💥");
        sendGameMessage(TextFormatting.DARK_RED + "Better luck next time...");

        // Create explosion at player's location
        if (world instanceof ServerWorld) {
            ServerWorld serverWorld = (ServerWorld) world;

            // Create explosion particles around the player
            BlockPos playerPos = player.blockPosition();
            for (int i = 0; i < 50; i++) {
                double x = playerPos.getX() + (Math.random() - 0.5) * 4;
                double y = playerPos.getY() + Math.random() * 3;
                double z = playerPos.getZ() + (Math.random() - 0.5) * 4;

                serverWorld.sendParticles(ParticleTypes.EXPLOSION, x, y, z, 1, 0, 0, 0, 0.1);
                serverWorld.sendParticles(ParticleTypes.LAVA, x, y, z, 1, 0, 0, 0, 0.1);
            }

            // Play explosion sound
            world.playSound(null, playerPos, SoundEvents.GENERIC_EXPLODE, SoundCategory.BLOCKS, 2.0f, 1.0f);

            // Damage the player (but don't kill them - this is for content!)
            if (player instanceof ServerPlayerEntity) {
                ServerPlayerEntity serverPlayer = (ServerPlayerEntity) player;
                serverPlayer.setHealth(1.0f); // Leave them with half a heart for drama!
            }
        }

        gameActive = false;
    }

    private void celebrateJackpot() {
        if (world instanceof ServerWorld) {
            ServerWorld serverWorld = (ServerWorld) world;

            // Create massive firework celebration around the slot machine
            for (int i = 0; i < 100; i++) {
                double x = gameOrigin.getX() + (Math.random() - 0.5) * 8;
                double y = gameOrigin.getY() + Math.random() * 6 + 2;
                double z = gameOrigin.getZ() + (Math.random() - 0.5) * 8;

                serverWorld.sendParticles(ParticleTypes.FIREWORK, x, y, z, 1, 0, 0, 0, 0.1);
                serverWorld.sendParticles(ParticleTypes.HAPPY_VILLAGER, x, y, z, 1, 0, 0, 0, 0.1);
            }

            // Play victory sounds
            world.playSound(null, gameOrigin, SoundEvents.UI_TOAST_CHALLENGE_COMPLETE, SoundCategory.BLOCKS, 2.0f, 1.0f);
            world.playSound(null, gameOrigin, SoundEvents.PLAYER_LEVELUP, SoundCategory.BLOCKS, 2.0f, 1.0f);
        }
    }

    public void endGame() {
        // Clear the entire game area
        for (int x = -3; x <= 6; x++) {
            for (int y = -2; y <= 5; y++) {
                for (int z = -11; z <= 2; z++) {
                    BlockPos pos = gameOrigin.offset(x, y, z);
                    world.setBlockAndUpdate(pos, Blocks.AIR.defaultBlockState());
                }
            }
        }

        gameActive = false;
        removePlayerGame(player);
        sendGameMessage("Slot Machine Challenge ended!");
    }

    public String getSlotResultsString() {
        if (!spinning && slotResults != null) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < SLOT_COUNT; i++) {
                switch (slotResults[i]) {
                    case 0: sb.append("💎"); break; // Diamond
                    case 1: sb.append("🟨"); break; // Gold
                    case 2: sb.append("⚪"); break; // Iron
                    case 3: sb.append("🟢"); break; // Emerald
                    case 4: sb.append("🔴"); break; // Redstone
                    case 5: sb.append("⚫"); break; // Coal
                }
                if (i < SLOT_COUNT - 1) sb.append(" | ");
            }
            return sb.toString();
        }
        return "🎰 | 🎰 | 🎰";
    }

    private void sendGameMessage(String message) {
        player.sendMessage(new StringTextComponent("[SlotMachine] " + message), player.getUUID());
    }

    // Getters
    public boolean isGameActive() { return gameActive; }
    public boolean isSpinning() { return spinning; }
    public PlayerEntity getPlayer() { return player; }
}
