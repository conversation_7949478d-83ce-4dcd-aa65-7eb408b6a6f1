package com.connect4.mod.commands;

import com.connect4.mod.game.Connect4Game;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.command.CommandSource;
import net.minecraft.command.Commands;
import net.minecraft.command.arguments.EntityArgument;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;

public class Connect4Commands {

    public static void register(CommandDispatcher<CommandSource> dispatcher) {
        dispatcher.register(Commands.literal("connect4")
            .then(Commands.literal("start")
                .then(Commands.argument("player2", EntityArgument.player())
                    .executes(Connect4Commands::startGame))
                .executes(Connect4Commands::startSoloGame))
            .then(Commands.literal("drop")
                .then(Commands.argument("column", IntegerArgumentType.integer(1, 7))
                    .executes(Connect4Commands::dropPiece)))
            .then(Commands.literal("reset")
                .executes(Connect4Commands::resetGame))
            .then(Commands.literal("end")
                .executes(Connect4Commands::endGame))
            .then(Commands.literal("status")
                .executes(Connect4Commands::gameStatus))
            .then(Commands.literal("help")
                .executes(Connect4Commands::showHelp)));
    }

    private static int startGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can start Connect 4 games!"));
            return 0;
        }

        ServerPlayerEntity player1 = (ServerPlayerEntity) context.getSource().getEntity();
        ServerPlayerEntity player2 = EntityArgument.getPlayer(context, "player2");

        if (player1.equals(player2)) {
            context.getSource().sendFailure(new StringTextComponent("You can't play against yourself! Use '/connect4 start' without arguments for solo mode."));
            return 0;
        }

        // Check if either player is already in a game
        if (Connect4Game.getPlayerGame(player1) != null) {
            context.getSource().sendFailure(new StringTextComponent("You are already in a Connect 4 game! Use /connect4 end to leave."));
            return 0;
        }

        if (Connect4Game.getPlayerGame(player2) != null) {
            context.getSource().sendFailure(new StringTextComponent(player2.getDisplayName().getString() + " is already in a Connect 4 game!"));
            return 0;
        }

        // Create game at player's location
        BlockPos gamePos = player1.blockPosition().offset(3, 0, 0);
        new Connect4Game(player1.level, gamePos, player1, player2);

        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.GREEN + "Connect 4 game started! " +
            TextFormatting.RED + player1.getDisplayName().getString() +
            TextFormatting.WHITE + " vs " +
            TextFormatting.BLUE + player2.getDisplayName().getString()), true);

        return 1;
    }

    private static int startSoloGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can start Connect 4 games!"));
            return 0;
        }

        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();

        // Check if player is already in a game
        if (Connect4Game.getPlayerGame(player) != null) {
            context.getSource().sendFailure(new StringTextComponent("You are already in a Connect 4 game! Use /connect4 end to leave."));
            return 0;
        }

        // Create solo game (player plays both sides)
        BlockPos gamePos = player.blockPosition().offset(3, 0, 0);
        new Connect4Game(player.level, gamePos, player, player, true); // true for solo mode

        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.GREEN + "Solo Connect 4 game started! " +
            TextFormatting.YELLOW + "You control both " +
            TextFormatting.RED + "RED" + TextFormatting.YELLOW + " and " +
            TextFormatting.BLUE + "BLUE" + TextFormatting.YELLOW + " pieces!"), true);

        return 1;
    }

    private static int dropPiece(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can drop pieces!"));
            return 0;
        }

        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        Connect4Game game = Connect4Game.getPlayerGame(player);

        if (game == null) {
            context.getSource().sendFailure(new StringTextComponent("You are not in a Connect 4 game! Use /connect4 start <player> to start one."));
            return 0;
        }

        if (game.getCurrentPlayer() != player && !game.isSoloMode()) {
            context.getSource().sendFailure(new StringTextComponent("It's not your turn!"));
            return 0;
        }

        if (game.isGameWon()) {
            context.getSource().sendFailure(new StringTextComponent("The game is already over! Use /connect4 reset to play again."));
            return 0;
        }

        int column = IntegerArgumentType.getInteger(context, "column") - 1; // Convert to 0-based

        if (game.dropPiece(column)) {
            context.getSource().sendSuccess(new StringTextComponent(
                TextFormatting.GREEN + "Piece dropped in column " + (column + 1) + "!"), false);
            return 1;
        } else {
            context.getSource().sendFailure(new StringTextComponent("Could not drop piece in column " + (column + 1) + "!"));
            return 0;
        }
    }

    private static int resetGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can reset games!"));
            return 0;
        }

        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        Connect4Game game = Connect4Game.getPlayerGame(player);

        if (game == null) {
            context.getSource().sendFailure(new StringTextComponent("You are not in a Connect 4 game!"));
            return 0;
        }

        game.resetGame();
        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.YELLOW + "Connect 4 game reset!"), true);

        return 1;
    }

    private static int endGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can end games!"));
            return 0;
        }

        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        Connect4Game game = Connect4Game.getPlayerGame(player);

        if (game == null) {
            context.getSource().sendFailure(new StringTextComponent("You are not in a Connect 4 game!"));
            return 0;
        }

        game.endGame();
        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.RED + "Connect 4 game ended!"), true);

        return 1;
    }

    private static int gameStatus(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can check game status!"));
            return 0;
        }

        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        Connect4Game game = Connect4Game.getPlayerGame(player);

        if (game == null) {
            context.getSource().sendSuccess(new StringTextComponent(
                TextFormatting.GRAY + "You are not in a Connect 4 game."), false);
            return 1;
        }

        String status = TextFormatting.AQUA + "=== Connect 4 Game Status ===\n";
        status += TextFormatting.RED + "Player 1 (RED): " + game.getPlayer1().getDisplayName().getString() + "\n";
        status += TextFormatting.BLUE + "Player 2 (BLUE): " + game.getPlayer2().getDisplayName().getString() + "\n";

        if (game.isGameWon()) {
            if (game.getWinner() != null) {
                status += TextFormatting.GOLD + "Winner: " + game.getWinner().getDisplayName().getString() + "\n";
            } else {
                status += TextFormatting.YELLOW + "Game ended in a tie!\n";
            }
        } else {
            status += TextFormatting.GREEN + "Current turn: " + game.getCurrentPlayer().getDisplayName().getString() + "\n";
        }

        context.getSource().sendSuccess(new StringTextComponent(status), false);
        return 1;
    }

    private static int showHelp(CommandContext<CommandSource> context) {
        String help = TextFormatting.AQUA + "=== Connect 4 Commands ===\n" +
                     TextFormatting.WHITE + "/connect4 start <player> - Start a new game with another player\n" +
                     "/connect4 start - Start a solo game (play against yourself!)\n" +
                     "/connect4 drop <column> - Drop a piece in column 1-7\n" +
                     "/connect4 reset - Reset the current game\n" +
                     "/connect4 end - End the current game\n" +
                     "/connect4 status - Check game status\n" +
                     "/connect4 help - Show this help message\n\n" +
                     TextFormatting.YELLOW + "How to play:\n" +
                     TextFormatting.WHITE + "- Take turns dropping pieces into columns\n" +
                     "- Get 4 in a row (horizontal, vertical, or diagonal) to win!\n" +
                     "- Solo mode: Control both RED and BLUE pieces\n" +
                     "- Perfect for YouTube content creation!";

        context.getSource().sendSuccess(new StringTextComponent(help), false);
        return 1;
    }
}
