package com.connect4.mod.commands;

import com.connect4.mod.game.TicTacToeGame;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.command.CommandSource;
import net.minecraft.command.Commands;
import net.minecraft.command.arguments.EntityArgument;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;

public class TicTacToeCommands {
    
    public static void register(CommandDispatcher<CommandSource> dispatcher) {
        dispatcher.register(Commands.literal("tictactoe")
            .then(Commands.literal("start")
                .then(Commands.argument("player2", EntityArgument.player())
                    .executes(TicTacToeCommands::startGame))
                .executes(TicTacToeCommands::startSoloGame))
            .then(Commands.literal("place")
                .then(Commands.argument("x", IntegerArgumentType.integer(1, 3))
                    .then(Commands.argument("y", IntegerArgumentType.integer(1, 3))
                        .executes(TicTacToeCommands::placePiece))))
            .then(Commands.literal("reset")
                .executes(TicTacToeCommands::resetGame))
            .then(Commands.literal("end")
                .executes(TicTacToeCommands::endGame))
            .then(Commands.literal("status")
                .executes(TicTacToeCommands::gameStatus))
            .then(Commands.literal("help")
                .executes(TicTacToeCommands::showHelp)));
    }
    
    private static int startGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can start Tic Tac Toe games!"));
            return 0;
        }
        
        ServerPlayerEntity player1 = (ServerPlayerEntity) context.getSource().getEntity();
        ServerPlayerEntity player2 = EntityArgument.getPlayer(context, "player2");
        
        if (player1.equals(player2)) {
            context.getSource().sendFailure(new StringTextComponent("You can't play against yourself! Use '/tictactoe start' without arguments for solo mode."));
            return 0;
        }
        
        // Check if either player is already in a game
        if (TicTacToeGame.getPlayerGame(player1) != null) {
            context.getSource().sendFailure(new StringTextComponent("You are already in a Tic Tac Toe game! Use /tictactoe end to leave."));
            return 0;
        }
        
        if (TicTacToeGame.getPlayerGame(player2) != null) {
            context.getSource().sendFailure(new StringTextComponent(player2.getDisplayName().getString() + " is already in a Tic Tac Toe game!"));
            return 0;
        }
        
        // Create game at player's location
        BlockPos gamePos = player1.blockPosition().offset(3, 0, 0);
        new TicTacToeGame(player1.level, gamePos, player1, player2);
        
        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.GREEN + "Tic Tac Toe game started! " + 
            TextFormatting.RED + player1.getDisplayName().getString() + " (X)" + 
            TextFormatting.WHITE + " vs " + 
            TextFormatting.BLUE + player2.getDisplayName().getString() + " (O)"), true);
        
        return 1;
    }
    
    private static int startSoloGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can start Tic Tac Toe games!"));
            return 0;
        }
        
        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        
        // Check if player is already in a game
        if (TicTacToeGame.getPlayerGame(player) != null) {
            context.getSource().sendFailure(new StringTextComponent("You are already in a Tic Tac Toe game! Use /tictactoe end to leave."));
            return 0;
        }
        
        // Create solo game (player plays both sides)
        BlockPos gamePos = player.blockPosition().offset(3, 0, 0);
        new TicTacToeGame(player.level, gamePos, player, player, true); // true for solo mode
        
        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.GREEN + "Solo Tic Tac Toe game started! " + 
            TextFormatting.YELLOW + "You control both " + 
            TextFormatting.RED + "X (RED)" + TextFormatting.YELLOW + " and " + 
            TextFormatting.BLUE + "O (BLUE)" + TextFormatting.YELLOW + " pieces!"), true);
        
        return 1;
    }
    
    private static int placePiece(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can place pieces!"));
            return 0;
        }
        
        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        TicTacToeGame game = TicTacToeGame.getPlayerGame(player);
        
        if (game == null) {
            context.getSource().sendFailure(new StringTextComponent("You are not in a Tic Tac Toe game! Use /tictactoe start <player> to start one."));
            return 0;
        }
        
        if (game.getCurrentPlayer() != player && !game.isSoloMode()) {
            context.getSource().sendFailure(new StringTextComponent("It's not your turn!"));
            return 0;
        }
        
        if (game.isGameWon()) {
            context.getSource().sendFailure(new StringTextComponent("The game is already over! Use /tictactoe reset to play again."));
            return 0;
        }
        
        int x = IntegerArgumentType.getInteger(context, "x") - 1; // Convert to 0-based
        int y = IntegerArgumentType.getInteger(context, "y") - 1; // Convert to 0-based
        
        if (game.placePiece(x, y)) {
            context.getSource().sendSuccess(new StringTextComponent(
                TextFormatting.GREEN + "Piece placed at (" + (x + 1) + "," + (y + 1) + ")!"), false);
            return 1;
        } else {
            context.getSource().sendFailure(new StringTextComponent("Could not place piece at (" + (x + 1) + "," + (y + 1) + ")!"));
            return 0;
        }
    }
    
    private static int resetGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can reset games!"));
            return 0;
        }
        
        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        TicTacToeGame game = TicTacToeGame.getPlayerGame(player);
        
        if (game == null) {
            context.getSource().sendFailure(new StringTextComponent("You are not in a Tic Tac Toe game!"));
            return 0;
        }
        
        game.resetGame();
        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.YELLOW + "Tic Tac Toe game reset!"), true);
        
        return 1;
    }
    
    private static int endGame(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can end games!"));
            return 0;
        }
        
        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        TicTacToeGame game = TicTacToeGame.getPlayerGame(player);
        
        if (game == null) {
            context.getSource().sendFailure(new StringTextComponent("You are not in a Tic Tac Toe game!"));
            return 0;
        }
        
        game.endGame();
        context.getSource().sendSuccess(new StringTextComponent(
            TextFormatting.RED + "Tic Tac Toe game ended!"), true);
        
        return 1;
    }
    
    private static int gameStatus(CommandContext<CommandSource> context) throws CommandSyntaxException {
        if (!(context.getSource().getEntity() instanceof ServerPlayerEntity)) {
            context.getSource().sendFailure(new StringTextComponent("Only players can check game status!"));
            return 0;
        }
        
        ServerPlayerEntity player = (ServerPlayerEntity) context.getSource().getEntity();
        TicTacToeGame game = TicTacToeGame.getPlayerGame(player);
        
        if (game == null) {
            context.getSource().sendSuccess(new StringTextComponent(
                TextFormatting.GRAY + "You are not in a Tic Tac Toe game."), false);
            return 1;
        }
        
        String status = TextFormatting.AQUA + "=== Tic Tac Toe Game Status ===\n";
        status += TextFormatting.RED + "Player 1 (X/RED): " + game.getPlayer1().getDisplayName().getString() + "\n";
        status += TextFormatting.BLUE + "Player 2 (O/BLUE): " + game.getPlayer2().getDisplayName().getString() + "\n";
        
        if (game.isGameWon()) {
            if (game.getWinner() != null) {
                status += TextFormatting.GOLD + "Winner: " + game.getWinner().getDisplayName().getString() + "\n";
            } else {
                status += TextFormatting.YELLOW + "Game ended in a tie!\n";
            }
        } else {
            status += TextFormatting.GREEN + "Current turn: " + game.getCurrentPlayer().getDisplayName().getString() + "\n";
        }
        
        context.getSource().sendSuccess(new StringTextComponent(status), false);
        return 1;
    }
    
    private static int showHelp(CommandContext<CommandSource> context) {
        String help = TextFormatting.AQUA + "=== Tic Tac Toe Commands ===\n" +
                     TextFormatting.WHITE + "/tictactoe start <player> - Start a new game with another player\n" +
                     "/tictactoe start - Start a solo game (play against yourself!)\n" +
                     "/tictactoe place <x> <y> - Place a piece at position (1-3, 1-3)\n" +
                     "/tictactoe reset - Reset the current game\n" +
                     "/tictactoe end - End the current game\n" +
                     "/tictactoe status - Check game status\n" +
                     "/tictactoe help - Show this help message\n\n" +
                     TextFormatting.YELLOW + "How to play:\n" +
                     TextFormatting.WHITE + "- Take turns placing X and O pieces\n" +
                     "- Get 3 in a row (horizontal, vertical, or diagonal) to win!\n" +
                     "- Solo mode: Control both X (RED) and O (BLUE) pieces\n" +
                     "- Perfect for YouTube content creation!";
        
        context.getSource().sendSuccess(new StringTextComponent(help), false);
        return 1;
    }
}
