package com.connect4.mod.game;

import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.particles.ParticleTypes;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvents;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.World;
import net.minecraft.world.server.ServerWorld;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

public class FallTrustGame {
    private static final int PLATFORM_SIZE = 5; // 5x5 platform of snow piles
    private static final int VOID_DEPTH = 20; // How deep the void trap goes

    // Game state
    private BlockPos gameOrigin;
    private World world;
    private PlayerEntity player1;
    private PlayerEntity player2;
    private boolean gameActive;
    private boolean gameStarted;
    private int player1TrapX, player1TrapZ; // Trap location under player 1's platform
    private int player2TrapX, player2TrapZ; // Trap location under player 2's platform
    private boolean player1Chosen, player2Chosen;
    private int player1ChoiceX, player1ChoiceZ;
    private int player2ChoiceX, player2ChoiceZ;
    private Random random;

    // Visual blocks
    private static final Block PLATFORM_BLOCK = Blocks.SNOW_BLOCK;
    private static final Block SPACING_BLOCK = Blocks.STONE;
    private static final Block FRAME_BLOCK = Blocks.YELLOW_CONCRETE;
    private static final Block BACKDROP_BLOCK = Blocks.BLACK_CONCRETE;
    private static final Block INDICATOR_BLOCK = Blocks.GLOWSTONE;

    // Static game instances
    private static Map<UUID, FallTrustGame> playerGames = new HashMap<>();

    public FallTrustGame(World world, BlockPos origin, PlayerEntity player1, PlayerEntity player2) {
        this.world = world;
        this.gameOrigin = origin;
        this.player1 = player1;
        this.player2 = player2;
        this.gameActive = true;
        this.gameStarted = false;
        this.player1Chosen = false;
        this.player2Chosen = false;
        this.random = new Random();

        // Generate random trap locations (0-4 for each platform)
        this.player1TrapX = random.nextInt(PLATFORM_SIZE);
        this.player1TrapZ = random.nextInt(PLATFORM_SIZE);
        this.player2TrapX = random.nextInt(PLATFORM_SIZE);
        this.player2TrapZ = random.nextInt(PLATFORM_SIZE);

        // Store game instances for both players
        playerGames.put(player1.getUUID(), this);
        playerGames.put(player2.getUUID(), this);

        buildGameArena();
        sendGameMessage("🕳️ Fall Trust Challenge started!");
        sendGameMessage("Each player has a 5x5 platform with ONE deadly trap!");
        sendGameMessage("You can see the OTHER player's trap, but not your own!");
        sendGameMessage("Tell each other where the trap is... truth or lie? 😈");
        sendPrivateMessage(player1, "🔍 " + player2.getDisplayName().getString() + "'s trap is at position (" + (player2TrapX + 1) + "," + (player2TrapZ + 1) + ")");
        sendPrivateMessage(player2, "🔍 " + player1.getDisplayName().getString() + "'s trap is at position (" + (player1TrapX + 1) + "," + (player1TrapZ + 1) + ")");
        sendGameMessage("Use /falltrust choose <x> <y> to pick your snow pile (1-5, 1-5)");
    }

    public static FallTrustGame getPlayerGame(PlayerEntity player) {
        return playerGames.get(player.getUUID());
    }

    public static void removePlayerGame(PlayerEntity player) {
        FallTrustGame game = playerGames.get(player.getUUID());
        if (game != null) {
            playerGames.remove(game.player1.getUUID());
            playerGames.remove(game.player2.getUUID());
        }
    }

    public BlockPos getGameOrigin() {
        return gameOrigin;
    }

    public void buildGameArena() {
        // Clear the entire game area
        for (int x = -10; x <= 20; x++) {
            for (int y = -25; y <= 10; y++) {
                for (int z = -10; z <= 20; z++) {
                    BlockPos pos = gameOrigin.offset(x, y, z);
                    world.setBlockAndUpdate(pos, Blocks.AIR.defaultBlockState());
                }
            }
        }

        // Build Player 1's platform (left side)
        buildPlayerPlatform(0, 0, player1TrapX, player1TrapZ, "Player 1");

        // Build Player 2's platform (right side)
        buildPlayerPlatform(15, 0, player2TrapX, player2TrapZ, "Player 2");

        // Build central observation area
        buildObservationArea();

        // Add backdrop
        buildBackdrop();

        // Add lighting
        addLighting();
    }

    private void buildPlayerPlatform(int offsetX, int offsetZ, int trapX, int trapZ, String playerName) {
        // Build the platform base
        for (int x = 0; x < PLATFORM_SIZE; x++) {
            for (int z = 0; z < PLATFORM_SIZE; z++) {
                BlockPos basePos = gameOrigin.offset(offsetX + x * 2, -1, offsetZ + z * 2);

                if (x == trapX && z == trapZ) {
                    // This is the trap location - create void
                    for (int depth = 0; depth < VOID_DEPTH; depth++) {
                        BlockPos voidPos = gameOrigin.offset(offsetX + x * 2, -2 - depth, offsetZ + z * 2);
                        world.setBlockAndUpdate(voidPos, Blocks.AIR.defaultBlockState());
                    }
                    // Add lava at the bottom for instant death
                    BlockPos lavaPos = gameOrigin.offset(offsetX + x * 2, -2 - VOID_DEPTH, offsetZ + z * 2);
                    world.setBlockAndUpdate(lavaPos, Blocks.LAVA.defaultBlockState());
                } else {
                    // Safe location - solid base
                    world.setBlockAndUpdate(basePos, FRAME_BLOCK.defaultBlockState());
                }

                // Add snow block on top
                BlockPos snowPos = gameOrigin.offset(offsetX + x * 2, 0, offsetZ + z * 2);
                world.setBlockAndUpdate(snowPos, PLATFORM_BLOCK.defaultBlockState());

                // Add stone spacing blocks
                if (x < PLATFORM_SIZE - 1) {
                    BlockPos spacingPos = gameOrigin.offset(offsetX + x * 2 + 1, 0, offsetZ + z * 2);
                    world.setBlockAndUpdate(spacingPos, SPACING_BLOCK.defaultBlockState());
                }
                if (z < PLATFORM_SIZE - 1) {
                    BlockPos spacingPos = gameOrigin.offset(offsetX + x * 2, 0, offsetZ + z * 2 + 1);
                    world.setBlockAndUpdate(spacingPos, SPACING_BLOCK.defaultBlockState());
                }
            }
        }

        // Add platform frame
        for (int x = -1; x <= PLATFORM_SIZE * 2; x++) {
            for (int z = -1; z <= PLATFORM_SIZE * 2; z++) {
                if (x == -1 || x == PLATFORM_SIZE * 2 || z == -1 || z == PLATFORM_SIZE * 2) {
                    BlockPos framePos = gameOrigin.offset(offsetX + x, -1, offsetZ + z);
                    world.setBlockAndUpdate(framePos, FRAME_BLOCK.defaultBlockState());
                }
            }
        }

        // Add player spawn platform
        BlockPos spawnPos = gameOrigin.offset(offsetX + PLATFORM_SIZE, 1, offsetZ + PLATFORM_SIZE);
        world.setBlockAndUpdate(spawnPos, FRAME_BLOCK.defaultBlockState());

        // Add sign with player name
        BlockPos signPos = gameOrigin.offset(offsetX + PLATFORM_SIZE, 2, offsetZ + PLATFORM_SIZE);
        world.setBlockAndUpdate(signPos, Blocks.OAK_SIGN.defaultBlockState());
    }

    private void buildObservationArea() {
        // Build central platform between the two player platforms
        int centerX = 7;
        int centerZ = 4;

        for (int x = 0; x <= 3; x++) {
            for (int z = 0; z <= 3; z++) {
                BlockPos platformPos = gameOrigin.offset(centerX + x, 0, centerZ + z);
                world.setBlockAndUpdate(platformPos, FRAME_BLOCK.defaultBlockState());
            }
        }

        // Add glowstone indicators
        BlockPos indicator1 = gameOrigin.offset(centerX + 1, 1, centerZ + 1);
        BlockPos indicator2 = gameOrigin.offset(centerX + 2, 1, centerZ + 2);
        world.setBlockAndUpdate(indicator1, INDICATOR_BLOCK.defaultBlockState());
        world.setBlockAndUpdate(indicator2, INDICATOR_BLOCK.defaultBlockState());

        // Add instruction sign
        BlockPos instructionSign = gameOrigin.offset(centerX + 1, 2, centerZ + 1);
        world.setBlockAndUpdate(instructionSign, Blocks.OAK_SIGN.defaultBlockState());
    }

    private void buildBackdrop() {
        // Add backdrop behind both platforms
        for (int x = -2; x <= 22; x++) {
            for (int y = -2; y <= 5; y++) {
                BlockPos backdropPos = gameOrigin.offset(x, y, -2);
                world.setBlockAndUpdate(backdropPos, BACKDROP_BLOCK.defaultBlockState());
            }
        }
    }

    private void addLighting() {
        // Add lighting around the arena
        for (int i = 0; i <= 4; i++) {
            // Player 1 platform lighting
            BlockPos light1 = gameOrigin.offset(-2, 2, i * 2);
            world.setBlockAndUpdate(light1, INDICATOR_BLOCK.defaultBlockState());

            // Player 2 platform lighting
            BlockPos light2 = gameOrigin.offset(17, 2, i * 2);
            world.setBlockAndUpdate(light2, INDICATOR_BLOCK.defaultBlockState());
        }
    }

    public boolean makeChoice(PlayerEntity player, int x, int z) {
        if (!gameActive || gameStarted) {
            return false;
        }

        // Convert to 0-based coordinates
        x = x - 1;
        z = z - 1;

        if (x < 0 || x >= PLATFORM_SIZE || z < 0 || z >= PLATFORM_SIZE) {
            sendPrivateMessage(player, "Invalid coordinates! Use 1-5 for both X and Z.");
            return false;
        }

        if (player == player1) {
            if (player1Chosen) {
                sendPrivateMessage(player, "You have already made your choice!");
                return false;
            }
            player1Chosen = true;
            player1ChoiceX = x;
            player1ChoiceZ = z;
            sendGameMessage(player1.getDisplayName().getString() + " has made their choice!");
        } else if (player == player2) {
            if (player2Chosen) {
                sendPrivateMessage(player, "You have already made your choice!");
                return false;
            }
            player2Chosen = true;
            player2ChoiceX = x;
            player2ChoiceZ = z;
            sendGameMessage(player2.getDisplayName().getString() + " has made their choice!");
        } else {
            return false;
        }

        // Check if both players have chosen
        if (player1Chosen && player2Chosen) {
            executeChoices();
        }

        return true;
    }

    private void executeChoices() {
        gameStarted = true;

        sendGameMessage("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        sendGameMessage("🕳️ BOTH PLAYERS HAVE CHOSEN! 🕳️");
        sendGameMessage("Time to reveal the truth...");
        sendGameMessage("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        // Reveal choices
        sendGameMessage(player1.getDisplayName().getString() + " chose position (" + (player1ChoiceX + 1) + "," + (player1ChoiceZ + 1) + ")");
        sendGameMessage(player2.getDisplayName().getString() + " chose position (" + (player2ChoiceX + 1) + "," + (player2ChoiceZ + 1) + ")");

        // Check results
        boolean player1Safe = !(player1ChoiceX == player1TrapX && player1ChoiceZ == player1TrapZ);
        boolean player2Safe = !(player2ChoiceX == player2TrapX && player2ChoiceZ == player2TrapZ);

        // Add dramatic pause
        world.getServer().execute(() -> {
            try {
                Thread.sleep(2000); // 2 second pause
                revealResults(player1Safe, player2Safe);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }

    private void revealResults(boolean player1Safe, boolean player2Safe) {
        sendGameMessage("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        sendGameMessage("🎯 REVEALING THE TRAPS! 🎯");
        sendGameMessage(player1.getDisplayName().getString() + "'s trap was at (" + (player1TrapX + 1) + "," + (player1TrapZ + 1) + ")");
        sendGameMessage(player2.getDisplayName().getString() + "'s trap was at (" + (player2TrapX + 1) + "," + (player2TrapZ + 1) + ")");
        sendGameMessage("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        // Execute the falls
        executePlayerFall(player1, player1Safe, 0, 0);
        executePlayerFall(player2, player2Safe, 15, 0);

        // Determine winner
        if (player1Safe && player2Safe) {
            sendGameMessage(TextFormatting.GOLD + "🎉 BOTH PLAYERS SURVIVED! 🎉");
            sendGameMessage(TextFormatting.GREEN + "Trust was maintained!");
        } else if (player1Safe && !player2Safe) {
            sendGameMessage(TextFormatting.GOLD + "🏆 " + player1.getDisplayName().getString() + " WINS! 🏆");
            sendGameMessage(TextFormatting.RED + player2.getDisplayName().getString() + " fell into the void!");
        } else if (!player1Safe && player2Safe) {
            sendGameMessage(TextFormatting.GOLD + "🏆 " + player2.getDisplayName().getString() + " WINS! 🏆");
            sendGameMessage(TextFormatting.RED + player1.getDisplayName().getString() + " fell into the void!");
        } else {
            sendGameMessage(TextFormatting.DARK_RED + "💀 BOTH PLAYERS FELL! 💀");
            sendGameMessage(TextFormatting.YELLOW + "Trust was completely broken!");
        }

        gameActive = false;
    }

    private void executePlayerFall(PlayerEntity player, boolean safe, int platformOffsetX, int platformOffsetZ) {
        int choiceX = (player == player1) ? player1ChoiceX : player2ChoiceX;
        int choiceZ = (player == player1) ? player1ChoiceZ : player2ChoiceZ;

        // Remove the snow block they chose
        BlockPos chosenPos = gameOrigin.offset(platformOffsetX + choiceX * 2, 0, platformOffsetZ + choiceZ * 2);
        world.setBlockAndUpdate(chosenPos, Blocks.AIR.defaultBlockState());

        if (safe) {
            // Safe landing - show they're safe
            sendPrivateMessage(player, TextFormatting.GREEN + "✅ You chose safely! The ground is solid!");

            // Add celebration particles
            if (world instanceof ServerWorld) {
                ServerWorld serverWorld = (ServerWorld) world;
                for (int i = 0; i < 20; i++) {
                    double x = chosenPos.getX() + Math.random();
                    double y = chosenPos.getY() + 1;
                    double z = chosenPos.getZ() + Math.random();
                    serverWorld.sendParticles(ParticleTypes.HAPPY_VILLAGER, x, y, z, 1, 0, 0, 0, 0.1);
                }
            }

            // Play success sound
            world.playSound(null, chosenPos, SoundEvents.PLAYER_LEVELUP, SoundCategory.BLOCKS, 1.0f, 1.0f);
        } else {
            // Deadly fall - they chose the trap
            sendPrivateMessage(player, TextFormatting.RED + "💀 You chose the TRAP! Falling into the void!");

            // Add dramatic falling particles
            if (world instanceof ServerWorld) {
                ServerWorld serverWorld = (ServerWorld) world;
                for (int i = 0; i < 50; i++) {
                    double x = chosenPos.getX() + Math.random();
                    double y = chosenPos.getY() + Math.random() * 3;
                    double z = chosenPos.getZ() + Math.random();
                    serverWorld.sendParticles(ParticleTypes.LAVA, x, y, z, 1, 0, 0, 0, 0.1);
                    serverWorld.sendParticles(ParticleTypes.SMOKE, x, y, z, 1, 0, 0, 0, 0.1);
                }
            }

            // Play falling/death sound
            world.playSound(null, chosenPos, SoundEvents.GENERIC_EXPLODE, SoundCategory.BLOCKS, 1.0f, 0.5f);

            // Teleport player to a safe location (don't actually kill them for content purposes)
            if (player instanceof ServerPlayerEntity) {
                ServerPlayerEntity serverPlayer = (ServerPlayerEntity) player;
                BlockPos safePos = gameOrigin.offset(10, 5, 5);
                serverPlayer.teleportTo(safePos.getX(), safePos.getY(), safePos.getZ());
                serverPlayer.setHealth(1.0f); // Leave them with half a heart for drama
            }
        }
    }

    public void endGame() {
        // Clear the entire game area
        for (int x = -10; x <= 20; x++) {
            for (int y = -25; y <= 10; y++) {
                for (int z = -10; z <= 20; z++) {
                    BlockPos pos = gameOrigin.offset(x, y, z);
                    world.setBlockAndUpdate(pos, Blocks.AIR.defaultBlockState());
                }
            }
        }

        gameActive = false;
        removePlayerGame(player1);
        sendGameMessage("Fall Trust Challenge ended!");
    }

    private void sendGameMessage(String message) {
        player1.sendMessage(new StringTextComponent("[FallTrust] " + message), player1.getUUID());
        player2.sendMessage(new StringTextComponent("[FallTrust] " + message), player2.getUUID());
    }

    private void sendPrivateMessage(PlayerEntity player, String message) {
        player.sendMessage(new StringTextComponent("[FallTrust] " + message), player.getUUID());
    }

    // Getters
    public boolean isGameActive() { return gameActive; }
    public boolean isGameStarted() { return gameStarted; }
    public PlayerEntity getPlayer1() { return player1; }
    public PlayerEntity getPlayer2() { return player2; }
}
