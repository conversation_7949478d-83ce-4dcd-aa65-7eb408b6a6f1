package com.connect4.mod.game;

import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.particles.ParticleTypes;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvents;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.World;
import net.minecraft.world.server.ServerWorld;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class Connect4Game {
    private static final int BOARD_WIDTH = 7;
    private static final int BOARD_HEIGHT = 6;

    // Game state
    private Block[][] board;
    private BlockPos gameOrigin;
    private World world;
    private PlayerEntity player1;
    private PlayerEntity player2;
    private PlayerEntity currentPlayer;
    private boolean gameActive;
    private boolean gameWon;
    private PlayerEntity winner;
    private boolean soloMode;

    // Visual blocks for the game
    private static final Block PLAYER1_BLOCK = Blocks.RED_CONCRETE;
    private static final Block PLAYER2_BLOCK = Blocks.BLUE_CONCRETE;
    private static final Block EMPTY_BLOCK = Blocks.AIR;
    private static final Block BOARD_FRAME = Blocks.YELLOW_CONCRETE;

    // Static game instances for multiplayer support
    private static Map<UUID, Connect4Game> playerGames = new HashMap<>();

    public Connect4Game(World world, BlockPos origin, PlayerEntity player1, PlayerEntity player2) {
        this(world, origin, player1, player2, false);
    }

    public Connect4Game(World world, BlockPos origin, PlayerEntity player1, PlayerEntity player2, boolean soloMode) {
        this.world = world;
        this.gameOrigin = origin;
        this.player1 = player1;
        this.player2 = player2;
        this.currentPlayer = player1;
        this.gameActive = true;
        this.gameWon = false;
        this.soloMode = soloMode;
        this.board = new Block[BOARD_WIDTH][BOARD_HEIGHT];

        // Initialize empty board
        for (int x = 0; x < BOARD_WIDTH; x++) {
            for (int y = 0; y < BOARD_HEIGHT; y++) {
                board[x][y] = EMPTY_BLOCK;
            }
        }

        // Store game instances for both players
        playerGames.put(player1.getUUID(), this);
        if (!soloMode) {
            playerGames.put(player2.getUUID(), this);
        }

        buildGameBoard();
        if (soloMode) {
            sendGameMessage("Solo Connect 4 game started! You control both RED and BLUE pieces.");
            sendGameMessage("Current turn: " + (currentPlayer == player1 ? "RED" : "BLUE"));
        } else {
            sendGameMessage("Connect 4 game started! " + player1.getDisplayName().getString() + " (RED) vs " + player2.getDisplayName().getString() + " (BLUE)");
            sendGameMessage(currentPlayer.getDisplayName().getString() + "'s turn!");
        }
    }

    public BlockPos getGameOrigin() {
        return gameOrigin;
    }

    public int getColumnFromPosition(BlockPos pos) {
        int relativeX = pos.getX() - gameOrigin.getX();
        int relativeZ = pos.getZ() - gameOrigin.getZ();

        // Check if clicking on the glowstone indicators (at z = -7)
        if (relativeZ == -7 && relativeX >= 0 && relativeX < BOARD_WIDTH) {
            return relativeX;
        }
        return -1;
    }

    public static Connect4Game getPlayerGame(PlayerEntity player) {
        return playerGames.get(player.getUUID());
    }

    public static void removePlayerGame(PlayerEntity player) {
        Connect4Game game = playerGames.get(player.getUUID());
        if (game != null) {
            playerGames.remove(game.player1.getUUID());
            playerGames.remove(game.player2.getUUID());
        }
    }

    public void buildGameBoard() {
        // Clear the entire game area first (expanded for player space and backdrop)
        for (int x = -3; x <= BOARD_WIDTH + 2; x++) {
            for (int y = -2; y <= BOARD_HEIGHT + 3; y++) {
                for (int z = -11; z <= 4; z++) {
                    BlockPos pos = gameOrigin.offset(x, y, z);
                    world.setBlockAndUpdate(pos, EMPTY_BLOCK.defaultBlockState());
                }
            }
        }

        // Build only the base platform (bottom frame)
        for (int x = -1; x <= BOARD_WIDTH; x++) {
            for (int z = -1; z <= 1; z++) {
                BlockPos pos = gameOrigin.offset(x, -1, z);
                world.setBlockAndUpdate(pos, BOARD_FRAME.defaultBlockState());
            }
        }

        // Build side pillars (left and right) - only at the edges for reference
        for (int y = 0; y <= BOARD_HEIGHT; y++) {
            // Left pillar
            BlockPos leftPos = gameOrigin.offset(-1, y, 0);
            world.setBlockAndUpdate(leftPos, BOARD_FRAME.defaultBlockState());

            // Right pillar
            BlockPos rightPos = gameOrigin.offset(BOARD_WIDTH, y, 0);
            world.setBlockAndUpdate(rightPos, BOARD_FRAME.defaultBlockState());
        }

        // Create extended player standing area (7 blocks away from the bottom)
        // Build a larger platform for the player to stand on
        for (int x = -2; x <= BOARD_WIDTH + 1; x++) {
            for (int z = -10; z <= -5; z++) {
                BlockPos platformPos = gameOrigin.offset(x, -1, z);
                world.setBlockAndUpdate(platformPos, BOARD_FRAME.defaultBlockState());
            }
        }

        // Add raised edges around the player platform for a professional look
        for (int x = -2; x <= BOARD_WIDTH + 1; x++) {
            // Back edge
            BlockPos backEdge = gameOrigin.offset(x, 0, -10);
            world.setBlockAndUpdate(backEdge, BOARD_FRAME.defaultBlockState());
            // Front edge
            BlockPos frontEdge = gameOrigin.offset(x, 0, -5);
            world.setBlockAndUpdate(frontEdge, BOARD_FRAME.defaultBlockState());
        }
        for (int z = -10; z <= -5; z++) {
            // Left edge
            BlockPos leftEdge = gameOrigin.offset(-2, 0, z);
            world.setBlockAndUpdate(leftEdge, BOARD_FRAME.defaultBlockState());
            // Right edge
            BlockPos rightEdge = gameOrigin.offset(BOARD_WIDTH + 1, 0, z);
            world.setBlockAndUpdate(rightEdge, BOARD_FRAME.defaultBlockState());
        }

        // Add column indicators at player level (7 blocks away from game board)
        for (int x = 0; x < BOARD_WIDTH; x++) {
            BlockPos indicatorPos = gameOrigin.offset(x, 0, -7); // At player eye level
            world.setBlockAndUpdate(indicatorPos, Blocks.GLOWSTONE.defaultBlockState());
        }

        // Add number signs above the glowstone indicators for easy reference
        for (int x = 0; x < BOARD_WIDTH; x++) {
            BlockPos signPos = gameOrigin.offset(x, 1, -7);
            world.setBlockAndUpdate(signPos, Blocks.OAK_SIGN.defaultBlockState());
        }

        // Add some lighting around the player area
        BlockPos leftLight = gameOrigin.offset(-1, 1, -7);
        BlockPos rightLight = gameOrigin.offset(BOARD_WIDTH, 1, -7);
        world.setBlockAndUpdate(leftLight, Blocks.GLOWSTONE.defaultBlockState());
        world.setBlockAndUpdate(rightLight, Blocks.GLOWSTONE.defaultBlockState());

        // Create backdrop/back splash for the Connect 4 board
        // Build a wall behind the game board for visual appeal
        for (int x = -1; x <= BOARD_WIDTH; x++) {
            for (int y = -1; y <= BOARD_HEIGHT + 1; y++) {
                BlockPos backdropPos = gameOrigin.offset(x, y, 2);
                world.setBlockAndUpdate(backdropPos, BOARD_FRAME.defaultBlockState());
            }
        }

        // Add decorative elements to the backdrop
        // Create a pattern or border on the backdrop
        for (int x = 0; x < BOARD_WIDTH; x++) {
            // Top border
            BlockPos topBorder = gameOrigin.offset(x, BOARD_HEIGHT + 1, 2);
            world.setBlockAndUpdate(topBorder, Blocks.GOLD_BLOCK.defaultBlockState());

            // Bottom border
            BlockPos bottomBorder = gameOrigin.offset(x, -1, 2);
            world.setBlockAndUpdate(bottomBorder, Blocks.GOLD_BLOCK.defaultBlockState());
        }

        // Side borders on backdrop
        for (int y = 0; y <= BOARD_HEIGHT; y++) {
            // Left border
            BlockPos leftBorder = gameOrigin.offset(-1, y, 2);
            world.setBlockAndUpdate(leftBorder, Blocks.GOLD_BLOCK.defaultBlockState());

            // Right border
            BlockPos rightBorder = gameOrigin.offset(BOARD_WIDTH, y, 2);
            world.setBlockAndUpdate(rightBorder, Blocks.GOLD_BLOCK.defaultBlockState());
        }

        // Add corner accents
        BlockPos topLeft = gameOrigin.offset(-1, BOARD_HEIGHT + 1, 2);
        BlockPos topRight = gameOrigin.offset(BOARD_WIDTH, BOARD_HEIGHT + 1, 2);
        BlockPos bottomLeft = gameOrigin.offset(-1, -1, 2);
        BlockPos bottomRight = gameOrigin.offset(BOARD_WIDTH, -1, 2);

        world.setBlockAndUpdate(topLeft, Blocks.GOLD_BLOCK.defaultBlockState());
        world.setBlockAndUpdate(topRight, Blocks.GOLD_BLOCK.defaultBlockState());
        world.setBlockAndUpdate(bottomLeft, Blocks.GOLD_BLOCK.defaultBlockState());
        world.setBlockAndUpdate(bottomRight, Blocks.GOLD_BLOCK.defaultBlockState());
    }

    public boolean dropPiece(int column) {
        if (!gameActive || gameWon || column < 0 || column >= BOARD_WIDTH) {
            return false;
        }

        // Find the lowest empty spot in the column
        int row = -1;
        for (int y = 0; y < BOARD_HEIGHT; y++) {
            if (board[column][y] == EMPTY_BLOCK) {
                row = y;
                break;
            }
        }

        if (row == -1) {
            sendGameMessage("Column " + (column + 1) + " is full!");
            return false;
        }

        // Place the piece
        Block pieceBlock = (currentPlayer == player1) ? PLAYER1_BLOCK : PLAYER2_BLOCK;
        board[column][row] = pieceBlock;

        // Update the world
        BlockPos piecePos = gameOrigin.offset(column, row, 0);
        world.setBlockAndUpdate(piecePos, pieceBlock.defaultBlockState());

        // Play sound effect
        world.playSound(null, piecePos, SoundEvents.STONE_PLACE, SoundCategory.BLOCKS, 1.0f, 1.0f);

        // Add particle effects
        if (world instanceof ServerWorld) {
            ServerWorld serverWorld = (ServerWorld) world;
            serverWorld.sendParticles(ParticleTypes.HAPPY_VILLAGER,
                piecePos.getX() + 0.5, piecePos.getY() + 1.0, piecePos.getZ() + 0.5,
                10, 0.5, 0.5, 0.5, 0.1);
        }

        // Check for win
        if (checkWin(column, row)) {
            gameWon = true;
            winner = currentPlayer;
            sendGameMessage(TextFormatting.GOLD + "🎉 " + winner.getDisplayName().getString() + " WINS! 🎉");
            celebrateWin();
            return true;
        }

        // Check for tie
        if (isBoardFull()) {
            gameWon = true;
            sendGameMessage(TextFormatting.YELLOW + "It's a tie! Good game!");
            return true;
        }

        // Switch players
        currentPlayer = (currentPlayer == player1) ? player2 : player1;
        String playerColor = (currentPlayer == player1) ? TextFormatting.RED + "RED" : TextFormatting.BLUE + "BLUE";

        if (soloMode) {
            sendGameMessage("Your turn: " + playerColor + TextFormatting.RESET + " pieces");
        } else {
            sendGameMessage(currentPlayer.getDisplayName().getString() + "'s turn! (" + playerColor + TextFormatting.RESET + ")");
        }

        return true;
    }

    private boolean checkWin(int col, int row) {
        Block piece = board[col][row];

        // Check horizontal
        if (checkDirection(col, row, 1, 0, piece) >= 4) return true;
        // Check vertical
        if (checkDirection(col, row, 0, 1, piece) >= 4) return true;
        // Check diagonal /
        if (checkDirection(col, row, 1, 1, piece) >= 4) return true;
        // Check diagonal \
        if (checkDirection(col, row, 1, -1, piece) >= 4) return true;

        return false;
    }

    private int checkDirection(int col, int row, int deltaX, int deltaY, Block piece) {
        int count = 1; // Count the piece itself

        // Check positive direction
        for (int i = 1; i < 4; i++) {
            int newCol = col + i * deltaX;
            int newRow = row + i * deltaY;
            if (newCol >= 0 && newCol < BOARD_WIDTH && newRow >= 0 && newRow < BOARD_HEIGHT &&
                board[newCol][newRow] == piece) {
                count++;
            } else {
                break;
            }
        }

        // Check negative direction
        for (int i = 1; i < 4; i++) {
            int newCol = col - i * deltaX;
            int newRow = row - i * deltaY;
            if (newCol >= 0 && newCol < BOARD_WIDTH && newRow >= 0 && newRow < BOARD_HEIGHT &&
                board[newCol][newRow] == piece) {
                count++;
            } else {
                break;
            }
        }

        return count;
    }

    private boolean isBoardFull() {
        for (int x = 0; x < BOARD_WIDTH; x++) {
            if (board[x][BOARD_HEIGHT - 1] == EMPTY_BLOCK) {
                return false;
            }
        }
        return true;
    }

    private void celebrateWin() {
        if (world instanceof ServerWorld) {
            ServerWorld serverWorld = (ServerWorld) world;

            // Create firework-like particle effects around the board
            for (int i = 0; i < 50; i++) {
                double x = gameOrigin.getX() + Math.random() * BOARD_WIDTH;
                double y = gameOrigin.getY() + Math.random() * BOARD_HEIGHT + 2;
                double z = gameOrigin.getZ() + (Math.random() - 0.5) * 3;

                serverWorld.sendParticles(ParticleTypes.FIREWORK, x, y, z, 1, 0, 0, 0, 0.1);
            }

            // Play victory sound
            world.playSound(null, gameOrigin, SoundEvents.UI_TOAST_CHALLENGE_COMPLETE,
                SoundCategory.BLOCKS, 2.0f, 1.0f);
        }
    }

    public void resetGame() {
        // Clear the board
        for (int x = 0; x < BOARD_WIDTH; x++) {
            for (int y = 0; y < BOARD_HEIGHT; y++) {
                board[x][y] = EMPTY_BLOCK;
                BlockPos pos = gameOrigin.offset(x, y, 0);
                world.setBlockAndUpdate(pos, EMPTY_BLOCK.defaultBlockState());
            }
        }

        // Reset game state
        currentPlayer = player1;
        gameWon = false;
        winner = null;

        sendGameMessage("Game reset! " + currentPlayer.getDisplayName().getString() + "'s turn!");
    }

    public void endGame() {
        // Clear the entire game area (expanded to match buildGameBoard including backdrop)
        for (int x = -3; x <= BOARD_WIDTH + 2; x++) {
            for (int y = -2; y <= BOARD_HEIGHT + 3; y++) {
                for (int z = -11; z <= 4; z++) {
                    BlockPos pos = gameOrigin.offset(x, y, z);
                    world.setBlockAndUpdate(pos, EMPTY_BLOCK.defaultBlockState());
                }
            }
        }

        gameActive = false;
        removePlayerGame(player1);
        sendGameMessage("Game ended!");
    }

    private void sendGameMessage(String message) {
        player1.sendMessage(new StringTextComponent("[Connect4] " + message), player1.getUUID());
        if (!soloMode && player2 != player1) {
            player2.sendMessage(new StringTextComponent("[Connect4] " + message), player2.getUUID());
        }
    }

    // Getters
    public boolean isGameActive() { return gameActive; }
    public boolean isGameWon() { return gameWon; }
    public PlayerEntity getCurrentPlayer() { return currentPlayer; }
    public PlayerEntity getWinner() { return winner; }
    public PlayerEntity getPlayer1() { return player1; }
    public PlayerEntity getPlayer2() { return player2; }
    public boolean isSoloMode() { return soloMode; }
}
